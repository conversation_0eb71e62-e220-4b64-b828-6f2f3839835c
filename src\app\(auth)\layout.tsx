
'use client';

import {
  <PERSON>bar<PERSON>rov<PERSON>,
  Sidebar,
  <PERSON>barHeader,
  SidebarContent,
  SidebarMenu,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuGroup } from "@/components/ui/dropdown-menu";
import { Bell, Search, LogOut, ArrowRight, PanelLeft, MessageSquare, Send, X, Bot, Link as LinkIcon } from "lucide-react";
import NavLinks from "@/components/layout/nav-links";
import { Logo } from "@/components/icons";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-is-mobile";
import MobileNav from "@/components/layout/mobile-nav";
import { ErpStoreProvider } from '@/store/erp-store';
import { Sheet, SheetTrigger, SheetContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet";
import { useStore, type RolePermissions } from "@/store/erp-store";
import { useState, useRef, useEffect, useMemo } from "react";
import { getBotResponse, type BotResponse } from "@/lib/rule-based-bot";
import Link from 'next/link';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/contexts/auth-context';


const Notifications = () => {
    // This component is kept for potential future use, but currently has no function
    // as the AI-driven notifications were removed.
    const notifications: any[] = [];
    const router = useRouter();

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative rounded-full">
                <Bell className="h-5 w-5" />
                {notifications.length > 0 && (
                    <span className="absolute right-0 top-0 flex h-2 w-2">
                        <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-accent opacity-75"></span>
                        <span className="relative inline-flex h-2 w-2 rounded-full bg-accent"></span>
                    </span>
                )}
                <span className="sr-only">تبديل الإشعارات</span>
            </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>الإشعارات</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {notifications.length > 0 ? (
                 <DropdownMenuGroup>
                    {notifications.map((notification, index) => (
                        <DropdownMenuItem key={index} onSelect={() => router.push(notification.link)}>
                            <div className="flex items-start gap-3">
                                {notification.icon}
                                <div className="flex flex-col">
                                    <p className="text-sm font-semibold">{notification.title}</p>
                                    <p className="text-xs text-muted-foreground">{notification.description}</p>
                                </div>
                            </div>
                        </DropdownMenuItem>
                    ))}
                </DropdownMenuGroup>
            ) : (
                 <div className="p-4 text-sm text-center text-muted-foreground">لا توجد إشعارات حاليًا.</div>
            )}
           
            <DropdownMenuSeparator />
            <DropdownMenuItem onSelect={() => router.push('/dashboard')}>
                <span className="flex-grow text-center">عرض كل التنبيهات</span>
                <ArrowRight className="h-4 w-4" />
            </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

const UserMenu = ({ onLogout }: { onLogout: () => void }) => (
    <DropdownMenu>
        <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
            <Avatar className="h-8 w-8">
            <AvatarImage src="https://placehold.co/40x40.png" alt="@user" data-ai-hint="male person" />
            <AvatarFallback>AW</AvatarFallback>
            </Avatar>
            <span className="sr-only">تبديل قائمة المستخدم</span>
        </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
        <DropdownMenuLabel>حسابي</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>الإعدادات</DropdownMenuItem>
        <DropdownMenuItem>الدعم</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onLogout} className="text-destructive">
            <LogOut className="ml-2 h-4 w-4" />
            <span>تسجيل الخروج</span>
        </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
);

const SimpleChatbot = () => {
    type ChatMessage = { sender: 'user' | 'bot' } & BotResponse;
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [userInput, setUserInput] = useState('');
    const { customers, suppliers, inventory, cashboxTransactions, sales, purchases, returns, activityLog, permissions: allPermissions } = useStore();
    const chatBodyRef = useRef<HTMLDivElement>(null);
    const router = useRouter();

    const currentUserRole = 'مدير';
    const currentUserPermissions = useMemo(() => {
        if (!allPermissions) return null;
        return allPermissions[currentUserRole] as RolePermissions;
    }, [allPermissions, currentUserRole]);

     useEffect(() => {
        if (currentUserPermissions && messages.length === 0) {
            const initialMessage = getBotResponse('مرحبا', {
                customers, suppliers, inventory, cashboxTransactions, sales, purchases, returns, activityLog
            }, currentUserPermissions);
             setMessages([{ sender: 'bot', ...initialMessage }]);
        }
    }, [currentUserPermissions]);

    useEffect(() => {
        if (chatBodyRef.current) {
            chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
        }
    }, [messages]);

    const sendQuery = (query: string) => {
         if (!query.trim() || !currentUserPermissions) return;

        const userMessage: ChatMessage = { sender: 'user' as const, text: query, action: null };
        
        const botResponse = getBotResponse(query, {
            customers,
            suppliers,
            inventory,
            cashboxTransactions,
            sales,
            purchases,
            returns,
            activityLog,
        }, currentUserPermissions);

        const botMessage: ChatMessage = { sender: 'bot' as const, ...botResponse };

        setMessages(prev => [...prev, userMessage, botMessage]);
        setUserInput('');
    }

    const handleSendMessage = (e: React.FormEvent) => {
        e.preventDefault();
        sendQuery(userInput);
    };
    
    const handleActionClick = (action: Exclude<BotResponse['action'], null>) => {
        switch (action.type) {
            case 'navigate':
                router.push(action.payload.url);
                break;
            case 'suggest_options':
                // The click handler for suggestion buttons will call sendQuery directly
                break;
            default:
                console.warn('Unknown bot action:', action);
        }
    };

    return (
        <div className="flex flex-col h-full border-r bg-card">
            <main ref={chatBodyRef} className="flex-1 p-4 overflow-y-auto space-y-4 bg-background/50">
                {messages.map((msg, index) => (
                    <div key={index} className={`flex flex-col gap-2 ${msg.sender === 'user' ? 'items-end' : 'items-start'}`}>
                        <div className={`flex items-start gap-3 max-w-[90%] ${msg.sender === 'user' ? 'flex-row-reverse' : ''}`}>
                             {msg.sender === 'bot' && (
                                <Avatar className="h-8 w-8 border shrink-0">
                                    <AvatarFallback className="bg-primary text-primary-foreground text-sm">خ</AvatarFallback>
                                </Avatar>
                            )}
                            <div className={`rounded-lg px-3 py-2 text-sm shadow-sm ${msg.sender === 'user' ? 'bg-primary text-primary-foreground rounded-br-none' : 'bg-card border rounded-bl-none'}`}>
                               <div className="whitespace-pre-wrap">{msg.text}</div>
                            </div>
                        </div>
                         {msg.action && (
                             <div className={`flex flex-wrap gap-2 mt-2 ${msg.sender === 'user' ? 'justify-end' : 'justify-start ml-11'}`}>
                                {msg.action.type === 'navigate' && (
                                     <Button 
                                        variant="outline" 
                                        size="sm" 
                                        className="h-auto py-1 px-3" 
                                        onClick={() => handleActionClick(msg.action!)}
                                    >
                                        <LinkIcon className="ml-2 h-3 w-3" />
                                        {msg.action.label}
                                    </Button>
                                )}
                                {msg.action.type === 'suggest_options' && msg.action.options.map((opt, i) => (
                                     <Button 
                                        key={i}
                                        variant="outline" 
                                        size="sm" 
                                        className="h-auto py-1 px-3" 
                                        onClick={() => sendQuery(opt.query)}
                                    >
                                        {opt.label}
                                    </Button>
                                ))}
                            </div>
                        )}
                    </div>
                ))}
            </main>
            <footer className="p-3 border-t bg-card">
                <form onSubmit={handleSendMessage} className="flex gap-2 w-full">
                    <Input
                        value={userInput}
                        onChange={e => setUserInput(e.target.value)}
                        placeholder="اسأل الخال..."
                        autoComplete="off"
                        className="flex-grow bg-background"
                    />
                    <Button type="submit" size="icon">
                        <Send className="h-4 w-4" />
                    </Button>
                </form>
            </footer>
        </div>
    );
};


function AuthLayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { toggleSidebar } = useSidebar();
  const { logout } = useAuth();

  const handleLogout = () => {
    toast({
      title: "تم تسجيل الخروج",
      description: "نأمل أن نراك قريباً!",
    });
    logout();
  };

  return (
    <div className="flex h-screen w-full bg-gradient-to-br from-background via-background to-muted/20 overflow-hidden">
        {!isMobile && (
            <Sidebar className="border-r border-border/50 backdrop-blur-sm">
                <SidebarHeader className="border-b border-border/50">
                    <div className="flex items-center justify-between p-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary/10 rounded-xl">
                                <Logo className="h-8 w-8 text-primary" />
                            </div>
                            <div className="flex flex-col">
                                <h2 className="text-lg font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">شركة العالم</h2>
                                <p className="text-xs text-muted-foreground">نظام إدارة الصيدلية</p>
                            </div>
                        </div>
                        <SidebarTrigger className="hover-lift" />
                    </div>
                </SidebarHeader>
                <SidebarContent className="p-2">
                    <SidebarMenu className="space-y-1">
                        <NavLinks />
                    </SidebarMenu>
                </SidebarContent>
            </Sidebar>
        )}
    
        <div className="flex flex-col flex-1 min-w-0">
            <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b border-border/50 bg-card/80 backdrop-blur-md px-6 shrink-0 shadow-modern">
                {isMobile && (
                     <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-xl">
                            <Logo className="h-6 w-6 text-primary" />
                        </div>
                        <span className="font-bold text-lg bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">شركة العالم</span>
                    </div>
                )}
                <div className="ml-auto flex items-center gap-3">
                    <Notifications />
                    <UserMenu onLogout={handleLogout} />
                </div>
            </header>
            <main className="flex-1 overflow-y-auto bg-gradient-to-br from-background to-muted/10 mb-16 lg:mb-0">{children}</main>
             {isMobile && <MobileNav />}
        </div>
        
        <Sheet>
            <SheetTrigger asChild>
                <Button
                    size="icon"
                    className="fixed bottom-6 right-6 h-16 w-16 rounded-full shadow-lg z-50"
                >
                    <Bot className="h-8 w-8" />
                    <span className="sr-only">فتح المساعد</span>
                </Button>
            </SheetTrigger>
            <SheetContent className="w-[400px] sm:w-[540px] p-0 flex flex-col" side={isMobile ? 'bottom' : 'right'}>
                <SheetHeader className="p-4 border-b bg-muted/50">
                    <SheetTitle>
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary/10 rounded-full">
                                <Bot className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <h3 className="font-semibold text-base">الخال</h3>
                                <p className="text-xs text-muted-foreground">مساعدك الرقمي</p>
                            </div>
                        </div>
                    </SheetTitle>
                </SheetHeader>
                <SimpleChatbot />
            </SheetContent>
        </Sheet>
    </div>
  );
}


export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
    return (
        <ProtectedRoute>
            <ErpStoreProvider>
                <SidebarProvider>
                    <AuthLayoutContent>{children}</AuthLayoutContent>
                </SidebarProvider>
            </ErpStoreProvider>
        </ProtectedRoute>
    )
}
