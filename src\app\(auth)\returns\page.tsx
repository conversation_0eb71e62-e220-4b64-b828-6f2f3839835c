
"use client";

import { useState, useMemo } from "react";
import { createRoot } from 'react-dom/client';
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Save, History, Printer, List, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { InvoiceTemplate, type InvoiceTemplateProps } from "@/components/invoice/invoice-template";
import { useStore } from "@/store/erp-store";
import type { ReturnItem, ReturnRecord, SaleRecord, PurchaseRecord, SaleItem as InvoiceSaleItem, PurchaseItem as InvoicePurchaseItem } from "@/store/erp-store";


export default function ReturnsPage() {
    const {
        sales,
        purchases,
        returns,
        settings,
        addReturn,
        deleteReturn,
    } = useStore();

    const [invoiceSearch, setInvoiceSearch] = useState("");
    const [selectedInvoice, setSelectedInvoice] = useState<SaleRecord | PurchaseRecord | null>(null);
    const [returnItems, setReturnItems] = useState<ReturnItem[]>([]);
    const [refundMethod, setRefundMethod] = useState("credit"); // 'credit' or 'cash'
    const [returnReason, setReturnReason] = useState("");
    const [activeTab, setActiveTab] = useState("sales");
    const [historySearchTerm, setHistorySearchTerm] = useState("");
    const { toast } = useToast();

    const [isInvoiceListOpen, setIsInvoiceListOpen] = useState(false);

    const handleSearchInvoice = () => {
        const invoices = activeTab === 'sales' ? sales : purchases;
        const foundInvoice = invoices.find(inv => inv.id.toLowerCase() === invoiceSearch.toLowerCase());

        if (foundInvoice) {
            selectInvoice(foundInvoice);
        } else {
            toast({
                variant: "destructive",
                title: "لم يتم العثور على الفاتورة",
                description: `الرجاء التحقق من رقم الفاتورة (${invoiceSearch}) والمحاولة مرة أخرى.`,
            });
            setSelectedInvoice(null);
            setReturnItems([]);
        }
    };

    const selectInvoice = (invoice: SaleRecord | PurchaseRecord) => {
        setSelectedInvoice(invoice);
        setInvoiceSearch(invoice.id);
        const itemsToReturn = invoice.items.map((item: InvoiceSaleItem | InvoicePurchaseItem) => ({
            id: 'id' in item ? item.id : 0, // Product ID
            name: item.name,
            originalQuantity: item.quantity,
            originalBonus: item.bonus || 0,
            returnQuantity: 0,
            returnBonus: 0, // This will be calculated from total return quantity
            price: item.price,
            expDate: item.expDate || 'N/A',
            batchNumber: item.batchNumber || 'N/A',
        }));
        setReturnItems(itemsToReturn);
        setIsInvoiceListOpen(false);
    }
    
    const handleReturnQuantityChange = (id: number, batchNumber: string, value: number) => {
        const item = returnItems.find(i => i.id === id && i.batchNumber === batchNumber);
        if (!item) return;

        const totalOriginalQuantity = item.originalQuantity + item.originalBonus;
        const returnedQuantity = Number(value);
        if (isNaN(returnedQuantity) || returnedQuantity < 0) return;

        if (returnedQuantity > totalOriginalQuantity) {
             toast({ variant: "destructive", title: "كمية غير صالحة", description: "الكمية المرتجعة لا يمكن أن تكون أكبر من الكمية الأصلية." });
             return;
        }

        setReturnItems(items => items.map(i => {
            if (i.id === id && i.batchNumber === batchNumber) {
                 // The single input field now controls the total return quantity.
                 // We will separate it back into paid and bonus quantities during save.
                 const totalReturned = Number(value);
                 return { ...i, returnQuantity: totalReturned, returnBonus: 0 }; // Temporarily store all in returnQuantity
            }
            return i;
        }));
    };

    const handlePrint = (returnRecord: ReturnRecord) => {
        const returnInvoiceData: InvoiceTemplateProps = {
            invoiceNumber: returnRecord.id,
            issueDate: returnRecord.date,
            invoiceType: returnRecord.type === 'sales' ? 'إشعار مرتجع مبيعات' : 'إشعار مرتجع مشتريات',
            paymentMethod: 'N/A',
            region: '',
            customerName: returnRecord.customerOrSupplier,
            items: returnRecord.items.map((item, index) => ({
              seq: index + 1,
              name: item.name,
              quantity: item.returnQuantity + item.returnBonus,
              bonus: 0,
              price: item.price,
              exp: item.expDate,
              notes: `من الفاتورة: ${returnRecord.originalInvoiceId}`
            })),
            discount: 0,
            totalReturnedAmount: returnRecord.amount,
            notes: `سبب الإرجاع: ${returnRecord.notes || 'غير محدد'}`,
            companyInfo: settings.companyInfo,
            printLogoSvg: settings.printLogoSvg,
        };

        const printWindow = window.open('', '_blank', 'left=50,top=50,width=1000,height=800');
        if (!printWindow) return;

        const printDocument = printWindow.document;
        printDocument.write('<html><head><title>طباعة إشعار مرتجع</title>');
        const styles = Array.from(document.styleSheets)
            .map(s => (s.href ? `<link rel="stylesheet" href="${s.href}">` : ''))
            .join('');
        const tailwindStyles = document.querySelector('style[data-tailwind]');
        printDocument.write(styles);
        if (tailwindStyles) {
            printDocument.write('<style>' + tailwindStyles.innerHTML + '</style>');
        }
        printDocument.write('<style>@media print { @page { size: A4; margin: 0; } body { -webkit-print-color-adjust: exact; print-color-adjust: exact; margin: 1.6cm; } } </style>');
        printDocument.write('</head><body dir="rtl">');
        
        const printContentEl = printDocument.createElement('div');
        printDocument.body.appendChild(printContentEl);

        const root = createRoot(printContentEl);
        root.render(<InvoiceTemplate {...returnInvoiceData} />);
        
        setTimeout(() => {
            printDocument.close();
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    const handleDelete = (returnId: string) => {
        deleteReturn(returnId);
        toast({ title: 'تم الحذف', description: `تم حذف المرتجع رقم ${returnId} بنجاح.` });
    };

    const handleSaveReturn = () => {
        if (!selectedInvoice) return;
        
        // Items where the user has entered a return quantity > 0
        const itemsWithReturnQty = returnItems.filter(i => (i.returnQuantity || 0) > 0);
        
        if (itemsWithReturnQty.length === 0) {
             toast({ variant: "destructive", title: "لا توجد مواد مرتجعة", description: "الرجاء تحديد كمية المواد المراد إرجاعها." });
             return;
        }

        // --- Start of Advanced Net Price Calculation ---

        // 1. Calculate the total number of physical units delivered in the original invoice (paid + bonus)
        const totalOriginalUnits = selectedInvoice.items.reduce((acc, item) => {
            return acc + (item.quantity + (item.bonus || 0));
        }, 0);

        // 2. Get the actual amount paid by the customer for the entire invoice.
        const finalInvoiceAmount = selectedInvoice.totalAmount;

        // 3. Calculate the effective average price per physical unit.
        // This is the core of the average price method.
        const averagePricePerUnit = totalOriginalUnits > 0 ? finalInvoiceAmount / totalOriginalUnits : 0;
        
        // 4. Calculate the financial value of the returned items using the average price.
        const totalReturnedUnits = itemsWithReturnQty.reduce((acc, item) => acc + (item.returnQuantity || 0), 0);
        const totalReturnedValue = totalReturnedUnits * averagePricePerUnit;
        
        // 5. Separate the total returned quantity back into paid and bonus for record-keeping.
        const finalReturnItems: ReturnItem[] = itemsWithReturnQty.map(item => {
            const totalReturned = item.returnQuantity || 0;
            const returnBonus = Math.min(item.originalBonus, totalReturned);
            const returnQuantity = totalReturned - returnBonus;
            
            return { ...item, returnQuantity, returnBonus };
        });
        
        // --- End of Advanced Net Price Calculation ---
        
        const newReturn: ReturnRecord = {
            id: `RET-${activeTab.charAt(0).toUpperCase()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
            type: activeTab as 'sales' | 'purchase',
            originalInvoiceId: selectedInvoice.id,
            customerOrSupplier: activeTab === 'sales' ? (selectedInvoice as SaleRecord).customerName : (selectedInvoice as PurchaseRecord).supplierName,
            customerOrSupplierId: activeTab === 'sales' ? (selectedInvoice as SaleRecord).customerId : (selectedInvoice as PurchaseRecord).supplierId,
            date: new Date().toISOString().split('T')[0],
            amount: totalReturnedValue, // Use the accurately calculated net return value.
            items: finalReturnItems,
            notes: returnReason,
            refundMethod: refundMethod as 'cash' | 'credit',
        };

        addReturn(newReturn);

        toast({
            title: "تم حفظ المرتجع بنجاح",
            description: `تم تسجيل المرتجع بقيمة صافية ${totalReturnedValue.toLocaleString('ar-IQ')} د.ع وتحديث المخزون والحسابات.`,
        });

        handlePrint(newReturn);

        // Reset form
        setInvoiceSearch("");
        setSelectedInvoice(null);
        setReturnItems([]);
        setRefundMethod("credit");
        setReturnReason("");
    };

    const totalReturnedAmount = useMemo(() => {
        if (!selectedInvoice || returnItems.length === 0) return 0;

        const totalOriginalUnits = selectedInvoice.items.reduce((acc, item) => {
            return acc + (item.quantity + (item.bonus || 0));
        }, 0);

        const finalInvoiceAmount = selectedInvoice.totalAmount;
        const averagePricePerUnit = totalOriginalUnits > 0 ? finalInvoiceAmount / totalOriginalUnits : 0;
        
        const totalReturnedUnits = returnItems.reduce((acc, item) => acc + (item.returnQuantity || 0), 0);

        return totalReturnedUnits * averagePricePerUnit;
    }, [returnItems, selectedInvoice]);

    const filteredReturnsHistory = useMemo(() => {
        return returns.filter(rec => 
            rec.customerOrSupplier.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
            rec.originalInvoiceId.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
            rec.id.toLowerCase().includes(historySearchTerm.toLowerCase())
        );
    }, [returns, historySearchTerm]);
    
  return (
    <>
      <PageHeader
        title="معالجة المرتجعات"
        description="إدارة مرتجعات المبيعات والمشتريات وتعديل المخزون."
      />

      <Tabs value={activeTab} onValueChange={(value) => {
          setActiveTab(value);
          setInvoiceSearch("");
          setSelectedInvoice(null);
          setReturnItems([]);
      }} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="sales">مرتجع مبيعات</TabsTrigger>
            <TabsTrigger value="purchase">مرتجع مشتريات</TabsTrigger>
        </TabsList>
        <TabsContent value="sales">
            {renderReturnForm('sales')}
        </TabsContent>
        <TabsContent value="purchase">
            {renderReturnForm('purchase')}
        </TabsContent>
      </Tabs>
      
      <Card className="mt-8">
        <CardHeader>
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <History className="h-5 w-5 text-primary" />
                    <CardTitle>سجل المرتجعات</CardTitle>
                </div>
                 <div className="w-full max-w-sm">
                    <Input 
                        placeholder="بحث برقم المرتجع/الفاتورة أو اسم العميل..." 
                        value={historySearchTerm}
                        onChange={(e) => setHistorySearchTerm(e.target.value)}
                    />
                </div>
            </div>
        </CardHeader>
        <CardContent>
             <Table>
                <TableHeader>
                   <TableRow>
                      <TableHead>رقم المرتجع</TableHead>
                      <TableHead>النوع</TableHead>
                      <TableHead>رقم الفاتورة الأصلية</TableHead>
                      <TableHead>العميل / المورد</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead className="text-right">المبلغ المسترد</TableHead>
                      <TableHead className="text-right">إجراءات</TableHead>
                   </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredReturnsHistory.length === 0 ? (
                         <TableRow>
                            <TableCell colSpan={7} className="text-center h-24">
                                لا توجد مرتجعات تطابق بحثك.
                            </TableCell>
                        </TableRow>
                    ) : (
                        filteredReturnsHistory.slice().reverse().map((rec) => (
                           <TableRow key={rec.id}>
                              <TableCell className="font-medium">{rec.id}</TableCell>
                              <TableCell>{rec.type === 'sales' ? 'مبيعات' : 'مشتريات'}</TableCell>
                              <TableCell>{rec.originalInvoiceId}</TableCell>
                              <TableCell>{rec.customerOrSupplier}</TableCell>
                              <TableCell>{rec.date}</TableCell>
                              <TableCell className="text-right">{rec.amount.toLocaleString()} د.ع</TableCell>
                               <TableCell className="text-right">
                                <div className="flex gap-2 justify-end">
                                    <Button variant="outline" size="icon" onClick={() => handlePrint(rec)}>
                                        <Printer className="h-4 w-4" />
                                    </Button>
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="destructive" size="icon">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                            <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المرتجع بشكل دائم وعكس تأثيره على المخزون والحسابات.
                                            </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                            <AlertDialogAction onClick={() => handleDelete(rec.id)}>متابعة</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </div>
                              </TableCell>
                           </TableRow>
                        ))
                    )}
                </TableBody>
             </Table>
        </CardContent>
      </Card>
    </>
  );

  function renderReturnForm(type: 'sales' | 'purchase') {
    const invoices = type === 'sales' ? sales : purchases;
    return (
        <Card>
            <CardHeader>
                <CardTitle>إنشاء مرتجع {type === 'sales' ? 'مبيعات' : 'مشتريات'} جديد</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex gap-2 mb-6">
                    <div className="flex-grow space-y-2">
                        <Label htmlFor={`invoice-search-${type}`}>ابحث عن الفاتورة الأصلية</Label>
                        <Input 
                            id={`invoice-search-${type}`} 
                            placeholder="أدخل رقم الفاتورة..."
                            value={invoiceSearch}
                            onChange={(e) => setInvoiceSearch(e.target.value)}
                        />
                    </div>
                    <Button onClick={handleSearchInvoice} className="self-end">
                        <Search className="ml-2 h-4 w-4" />
                        بحث
                    </Button>
                    <Dialog open={isInvoiceListOpen} onOpenChange={setIsInvoiceListOpen}>
                        <DialogTrigger asChild>
                            <Button variant="outline" className="self-end">
                                <List className="ml-2 h-4 w-4" />
                                اختيار فاتورة
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                            <DialogHeader>
                                <DialogTitle>اختر فاتورة {type === 'sales' ? 'مبيعات' : 'مشتريات'}</DialogTitle>
                            </DialogHeader>
                            <div className="max-h-[60vh] overflow-y-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>رقم الفاتورة</TableHead>
                                            <TableHead>{type === 'sales' ? 'العميل' : 'المورد'}</TableHead>
                                            <TableHead>التاريخ</TableHead>
                                            <TableHead className="text-right">الإجمالي</TableHead>
                                            <TableHead></TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {invoices.map(inv => (
                                            <TableRow key={inv.id}>
                                                <TableCell>{inv.id}</TableCell>
                                                <TableCell>{(inv as SaleRecord).customerName || (inv as PurchaseRecord).supplierName}</TableCell>
                                                <TableCell>{inv.date}</TableCell>
                                                <TableCell className="text-right">{inv.totalAmount.toLocaleString()} د.ع</TableCell>
                                                <TableCell>
                                                    <Button size="sm" onClick={() => selectInvoice(inv)}>اختيار</Button>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>

                {selectedInvoice && (
                    <div className="space-y-6 border-t pt-6">
                        <div className="grid sm:grid-cols-2 gap-4 text-sm">
                            <p><strong>رقم الفاتورة:</strong> {selectedInvoice.id}</p>
                            <p><strong>{type === 'sales' ? 'العميل:' : 'المورد:'}</strong> {(selectedInvoice as SaleRecord).customerName || (selectedInvoice as PurchaseRecord).supplierName}</p>
                            <p><strong>التاريخ:</strong> {selectedInvoice.date}</p>
                            <p><strong>الإجمالي الأصلي:</strong> {selectedInvoice.totalAmount.toLocaleString()} د.ع</p>
                        </div>
                        
                        <div className="mt-4">
                            <Label>المواد المراد إرجاعها:</Label>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>المادة</TableHead>
                                        <TableHead className="text-center">الكمية الإجمالية المباعة</TableHead>
                                        <TableHead>الكمية المرتجعة</TableHead>
                                        <TableHead className="text-right">سعر الوحدة</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {returnItems.map(item => (
                                        <TableRow key={`${item.id}-${item.batchNumber}`}>
                                            <TableCell className="font-medium">{item.name}</TableCell>
                                            <TableCell className="text-center">{item.originalQuantity + item.originalBonus}</TableCell>
                                            <TableCell>
                                                <Input 
                                                    type="number"
                                                    defaultValue={0}
                                                    onChange={(e) => handleReturnQuantityChange(item.id, item.batchNumber, Number(e.target.value))}
                                                    max={item.originalQuantity + item.originalBonus}
                                                    min="0"
                                                    className="w-24 mx-auto"
                                                />
                                            </TableCell>
                                            <TableCell className="text-right">{item.price.toLocaleString()} د.ع</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        <div className="grid sm:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor={`refund-method-${type}`}>طريقة الاسترداد</Label>
                                <Select value={refundMethod} onValueChange={setRefundMethod}>
                                    <SelectTrigger id={`refund-method-${type}`}>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="credit">خصم من الحساب</SelectItem>
                                        <SelectItem value="cash">استرجاع نقدي</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor={`return-reason-${type}`}>سبب الإرجاع (اختياري)</Label>
                                <Input id={`return-reason-${type}`} value={returnReason} onChange={e => setReturnReason(e.target.value)} placeholder="مثال: تالف، انتهاء صلاحية..."/>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
            {selectedInvoice && (
                <CardFooter className="flex-col items-stretch gap-4">
                    <div className="flex justify-between font-bold text-lg border-t pt-4">
                        <span>إجمالي المبلغ المسترد الصافي</span>
                        <span className="text-destructive">{totalReturnedAmount.toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    <Button size="lg" onClick={handleSaveReturn}>
                        <Save className="ml-2 h-4 w-4" />
                        حفظ وطباعة
                    </Button>
                </CardFooter>
            )}
        </Card>
    );
  }
}
