
import type { Customer, Supplier, Product, CashboxTransaction, SaleRecord, PurchaseRecord, ReturnRecord, ActivityLog, RolePermissions, Module, Permission } from '@/store/erp-store';
import { differenceInDays, parseISO, subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isWithinInterval, format, parse } from 'date-fns';

interface DataSnapshot {
    customers: Customer[];
    suppliers: Supplier[];
    inventory: Product[];
    cashboxTransactions: CashboxTransaction[];
    sales: SaleRecord[];
    purchases: PurchaseRecord[];
    returns: ReturnRecord[];
    activityLog: ActivityLog[];
}

type BotAction = 
    | {
        type: 'navigate';
        label: string;
        payload: { url: string; };
    }
    | {
        type: 'suggest_options';
        options: { label: string; query: string }[];
    };


export interface BotResponse {
    text: string;
    action?: BotAction | null;
}

interface Rule {
    keywords: string[];
    handler: (input: string, data: DataSnapshot) => BotResponse | null;
    priority?: number; // Higher number means higher priority
    exact?: boolean; // If true, matches the exact keyword
    requiredPermission?: { module: Module, permission: Permission };
}

const LOW_STOCK_THRESHOLD = 20;

const getDateRange = (input: string): { range: { start: Date, end: Date }, periodText: string } | null => {
    const now = new Date();
    if (input.includes("اليوم")) return { range: { start: startOfDay(now), end: endOfDay(now) }, periodText: "اليوم" };
    if (input.includes("البارحة") || input.includes("أمس")) {
        const yesterday = subDays(now, 1);
        return { range: { start: startOfDay(yesterday), end: endOfDay(yesterday) }, periodText: "البارحة" };
    }
    if (input.includes("الاسبوع") || input.includes("الأسبوع")) {
        if(input.includes("الماضي")) return { range: { start: startOfWeek(subDays(now, 7)), end: endOfWeek(subDays(now, 7)) }, periodText: "الأسبوع الماضي" };
        return { range: { start: startOfWeek(now), end: endOfWeek(now) }, periodText: "هذا الأسبوع" };
    }
    if (input.includes("الشهر")) {
        if(input.includes("الماضي")) return { range: { start: startOfMonth(subDays(now, 30)), end: endOfMonth(subDays(now, 30)) }, periodText: "الشهر الماضي" };
        return { range: { start: startOfMonth(now), end: endOfMonth(now) }, periodText: "هذا الشهر" };
    }
    return null;
}

const rules: Rule[] = [
    {
        keywords: ["مرحبا", "اهلا", "أهلا", "السلام عليكم"],
        handler: () => ({ text: "أهلاً بك! أنا الخال، مساعدك الرقمي. يمكنك سؤالي عن الأرصدة، المخزون، الفواتير، وحتى آخر التعديلات في النظام.", action: null }),
    },
    {
        keywords: ["الخال"],
        exact: true,
        handler: () => ({ 
            text: "أنا هنا للمساعدة! ماذا تريد أن تعرف؟", 
            action: {
                type: 'suggest_options',
                options: [
                    { label: 'رصيد الصندوق', query: 'رصيد الصندوق' },
                    { label: 'قيمة المخزون', query: 'قيمة المخزون' },
                    { label: 'ديون العملاء', query: 'الديون' },
                    { label: 'آخر الأنشطة', query: 'آخر الأنشطة' },
                ]
            }
        })
    },
    {
        keywords: ["رصيد الصندوق", "رصيد الكاش"],
        requiredPermission: { module: 'cashbox', permission: 'read' },
        handler: (input, data) => {
            const totalIncome = data.cashboxTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
            const totalExpense = data.cashboxTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
            const currentBalance = totalIncome - totalExpense;
            return {
                text: `الرصيد الحالي في الصندوق هو: **${currentBalance.toLocaleString('ar-IQ')} د.ع**`,
                action: {
                    type: 'navigate',
                    label: 'عرض تفاصيل الصندوق',
                    payload: { url: '/cashbox' }
                }
            };
        }
    },
    {
        keywords: ["رصيد"],
        exact: true,
        handler: () => ({
            text: "لمعرفة رصيد جهة معينة، اكتب 'رصيد [اسم العميل/المورد]' أو اختر أحد الخيارات التالية:",
            action: {
                type: 'suggest_options',
                options: [
                    { label: "رصيد الصندوق", query: "رصيد الصندوق" },
                    { label: "أرصدة العملاء", query: "قائمة العملاء" },
                    { label: "أرصدة الموردين", query: "قائمة الموردين" },
                    { label: "ديون العملاء (أعلى 5)", query: "الديون" },
                    { label: "مستحقات الموردين (أعلى 5)", query: "المستحقات" },
                ]
            }
        })
    },
    {
        keywords: ["مخزون"],
        exact: true,
        handler: () => ({
            text: "ماذا تريد أن تعرف عن المخزون؟",
            action: {
                type: 'suggest_options',
                options: [
                    { label: "قيمة المخزون الإجمالية", query: "قيمة المخزون" },
                    { label: "كميات المواد", query: "قائمة المنتجات" },
                    { label: "مواد منتهية الصلاحية", query: "المواد منتهية الصلاحية" },
                    { label: "مواد ستنتهي صلاحيتها قريباً", query: "المواد التي ستنتهي صلاحيتها قريباً" },
                    { label: "مواد ذات كمية منخفضة", query: "المواد ذات الكمية المنخفضة" },
                ]
            }
        })
    },
    {
        keywords: ["رصيد", "حساب"],
        handler: (input, data) => {
            const potentialName = input.replace(/(رصيد|حساب|كم رصيد|ما هو رصيد|ما هو حساب)\s*/, '').trim();
            if (!potentialName) return null;

            const customer = data.customers.find(c => c.name.includes(potentialName));
            if (customer) {
                return {
                    text: `رصيد العميل "${customer.name}" هو: ${customer.balance.toLocaleString('ar-IQ')} د.ع`,
                    action: {
                        type: 'navigate',
                        label: 'عرض كشف الحساب',
                        payload: { url: `/reports?reportType=account&accountId=customer-${customer.id}` }
                    }
                };
            }

            const supplier = data.suppliers.find(s => s.name.includes(potentialName));
            if (supplier) {
                return {
                     text: `رصيد المورد "${supplier.name}" هو: ${supplier.balance.toLocaleString('ar-IQ')} د.ع`,
                     action: {
                        type: 'navigate',
                        label: 'عرض كشف الحساب',
                        payload: { url: `/reports?reportType=account&accountId=supplier-${supplier.id}` }
                    }
                };
            }
            
            return { text: `عذراً، لم أتمكن من العثور على عميل أو مورد بالاسم "${potentialName}".`, action: null };
        }
    },
    {
        keywords: ["كمية"],
        requiredPermission: { module: 'inventory', permission: 'read' },
        handler: (input, data) => {
            const productName = input.replace(/(كمية المخزون|مخزون|كمية)\s*/, "").trim();
             if (!productName) return { text: "لمعرفة كمية منتج، يرجى كتابة السؤال كالتالي:\n'كمية [اسم المنتج]'", action: null };

            const items = data.inventory.filter(i => i.name.includes(productName));
            if (items.length === 0) {
                return { text: `عذراً، لم يتم العثور على منتج بالاسم "${productName}".`, action: null };
            }
            const totalStock = items.reduce((acc, item) => acc + item.stock, 0);
            const stockInfo = items.map(item => `   • الوجبة ${item.batchNumber}: ${item.stock} قطعة`).join('\n');
            const firstItem = items[0];
            return {
                text: `الكمية الإجمالية من "${firstItem.name}" هي ${totalStock} قطعة.\n\nالتفاصيل:\n${stockInfo}`,
                action: {
                    type: 'navigate',
                    label: `عرض "${firstItem.name}" في المخزون`,
                    payload: { url: `/inventory` }
                }
            };
        }
    },
     {
        keywords: ["قيمة المخزون"],
        requiredPermission: { module: 'inventory', permission: 'read' },
        handler: (input, data) => {
            const totalValue = data.inventory.reduce((acc, item) => acc + (item.stock * item.purchasePrice), 0);
            return {
                text: `القيمة الإجمالية للمخزون الحالي بناءً على سعر الشراء هي:\n**${totalValue.toLocaleString('ar-IQ')} د.ع**`,
                action: { type: 'navigate', label: 'الانتقال إلى المخزون', payload: { url: '/inventory' } }
            }
        }
    },
     {
        keywords: ["فواتير غير مسددة", "ديون", "الديون"],
        requiredPermission: { module: 'cashbox', permission: 'read' },
        handler: (input, data) => {
            const customerNameMatch = input.match(/(?:للعميل|لـ)\s*([\u0600-\u06FF\s]+)/);
            const customerName = customerNameMatch ? customerNameMatch[1].trim() : null;

            if (customerName) {
                 const customer = data.customers.find(c => c.name.includes(customerName));
                if (!customer) return { text: `لم يتم العثور على عميل بالاسم "${customerName}"`, action: null };
                const unpaidInvoices = data.sales.filter(s => s.customerId === customer.id && (s.status === 'unpaid' || s.status === 'partial'));
                if (unpaidInvoices.length === 0) return { text: `لا توجد فواتير غير مسددة للعميل ${customer.name}.`, action: null };
                const responseText = unpaidInvoices.map(inv => `• فاتورة ${inv.id}\n  المتبقي: ${(inv.totalAmount - inv.paidAmount).toLocaleString('ar-IQ')} د.ع`).join('\n\n');
                return { 
                    text: `الفواتير غير المسددة للعميل ${customer.name}:\n\n` + responseText,
                    action: {
                        type: 'navigate',
                        label: 'عرض كشف الحساب الكامل',
                        payload: { url: `/reports?reportType=account&accountId=customer-${customer.id}` }
                    }
                };
            }

            if (input.includes("اكثر") || input.includes("أعلى") || input.includes("الديون")) {
                 const topDebtors = data.customers
                    .filter(c => c.balance > 0)
                    .sort((a,b) => b.balance - a.balance)
                    .slice(0, 5);
                if (topDebtors.length === 0) return { text: "لا يوجد عملاء لديهم ديون حالياً.", action: null };
                const responseText = topDebtors.map((c, i) => `${i+1}. ${c.name} - ${c.balance.toLocaleString('ar-IQ')} د.ع`).join('\n');
                return { text: "أعلى 5 عملاء عليهم ديون:\n\n" + responseText, action: {
                    type: 'navigate',
                    label: 'عرض كل الديون',
                    payload: { url: '/cashbox' }
                } };
            }

            const unpaidSales = data.sales.filter(s => s.status === 'unpaid' || s.status === 'partial').length;
            const unpaidPurchases = data.purchases.filter(p => p.status === 'unpaid' || p.status === 'partial').length;
            if(unpaidSales === 0 && unpaidPurchases === 0) return { text: "لا توجد أي فواتير غير مسددة حالياً.", action: null };
            return {
                text: `يوجد حالياً:\n• ${unpaidSales} فاتورة مبيعات غير مسددة.\n• ${unpaidPurchases} فاتورة مشتريات غير مسددة.`,
                action: {
                    type: 'navigate',
                    label: 'عرض الفواتير غير المسددة',
                    payload: { url: '/cashbox' }
                }
            };
        }
    },
     {
        keywords: ["المستحقات", "ديوني للموردين"],
        requiredPermission: { module: 'cashbox', permission: 'read' },
        handler: (input, data) => {
             const topCreditors = data.suppliers
                .filter(s => s.balance > 0)
                .sort((a,b) => b.balance - a.balance)
                .slice(0, 5);
            if (topCreditors.length === 0) return { text: "لا توجد أي مستحقات للموردين حالياً.", action: null };
            const responseText = topCreditors.map((s, i) => `${i+1}. ${s.name} - ${s.balance.toLocaleString('ar-IQ')} د.ع`).join('\n');
            return { text: "أعلى 5 موردين لهم مستحقات:\n\n" + responseText, action: {
                 type: 'navigate',
                 label: 'عرض كل المستحقات',
                 payload: { url: '/cashbox' }
            }};
        }
    },
    {
        keywords: ["فاتورة"],
        handler: (input, data) => {
            const invoiceIdMatch = input.match(/([a-zA-Z]+-?\d+)/);
            if (!invoiceIdMatch) return null;

            const invoiceId = invoiceIdMatch[1].toUpperCase();
            const sale = data.sales.find(s => s.id.toUpperCase() === invoiceId);
            if (sale) {
                return {
                    text: `ملخص فاتورة المبيعات رقم ${sale.id}:\n• العميل: ${sale.customerName}\n• التاريخ: ${sale.date}\n• الإجمالي: ${sale.totalAmount.toLocaleString('ar-IQ')} د.ع\n• الحالة: ${sale.status}`,
                    action: {
                        type: 'navigate',
                        label: 'عرض كشف حساب العميل',
                        payload: { url: `/reports?reportType=account&accountId=customer-${sale.customerId}` }
                    }
                };
            }

            const purchase = data.purchases.find(p => p.id.toUpperCase() === invoiceId);
            if (purchase) {
                return {
                    text: `ملخص فاتورة المشتريات رقم ${purchase.id}:\n• المورد: ${purchase.supplierName}\n• التاريخ: ${purchase.date}\n• الإجمالي: ${purchase.totalAmount.toLocaleString('ar-IQ')} د.ع\n• الحالة: ${purchase.status}`,
                     action: {
                        type: 'navigate',
                        label: 'عرض كشف حساب المورد',
                        payload: { url: `/reports?reportType=account&accountId=supplier-${purchase.supplierId}` }
                    }
                };
            }
            
            return { text: `عذراً، لم أجد فاتورة بالرقم ${invoiceId}.`, action: null };
        }
    },
    {
        keywords: ["مبيعات", "مشتريات", "مرتجعات"],
        handler: (input, data) => {
            const dateInfo = getDateRange(input);
            if(!dateInfo) return null;
            
            if (input.includes("مبيعات")) {
                const salesToAnalyze = data.sales.filter(s => isWithinInterval(parse(s.date, 'yyyy-MM-dd', new Date()), dateInfo.range));
                if (salesToAnalyze.length === 0) return { text: `لا توجد مبيعات مسجلة خلال ${dateInfo.periodText}.`, action: null };
                const total = salesToAnalyze.reduce((acc, s) => acc + s.totalAmount, 0);
                return { text: `ملخص المبيعات خلال ${dateInfo.periodText}:\n• عدد الفواتير: ${salesToAnalyze.length}\n• إجمالي المبيعات: ${total.toLocaleString('ar-IQ')} د.ع`, action: null };
            }
            if (input.includes("مشتريات")) {
                 const purchasesToAnalyze = data.purchases.filter(p => isWithinInterval(parse(p.date, 'yyyy-MM-dd', new Date()), dateInfo.range));
                if (purchasesToAnalyze.length === 0) return { text: `لا توجد مشتريات مسجلة خلال ${dateInfo.periodText}.`, action: null };
                const total = purchasesToAnalyze.reduce((acc, p) => acc + p.totalAmount, 0);
                return { text: `ملخص المشتريات خلال ${dateInfo.periodText}:\n• عدد الفواتير: ${purchasesToAnalyze.length}\n• إجمالي المشتريات: ${total.toLocaleString('ar-IQ')} د.ع`, action: null };
            }
            if (input.includes("مرتجعات")) {
                 const returnsToAnalyze = data.returns.filter(r => isWithinInterval(parse(r.date, 'yyyy-MM-dd', new Date()), dateInfo.range));
                if (returnsToAnalyze.length === 0) return { text: `لا توجد مرتجعات مسجلة خلال ${dateInfo.periodText}.`, action: null };
                const total = returnsToAnalyze.reduce((acc, r) => acc + r.amount, 0);
                 const salesReturns = returnsToAnalyze.filter(r => r.type === 'sales').length;
                const purchaseReturns = returnsToAnalyze.filter(r => r.type === 'purchase').length;
                return { text: `ملخص المرتجعات خلال ${dateInfo.periodText}:\n• مرتجعات مبيعات: ${salesReturns}\n• مرتجعات مشتريات: ${purchaseReturns}\n• إجمالي قيمة المرتجعات: ${total.toLocaleString('ar-IQ')} د.ع`, action: null };
            }

            return null;
        }
    },
     {
        keywords: ["أرباح", "ارباح"],
        requiredPermission: { module: 'reports', permission: 'read' },
        handler: (input, data) => {
             const dateInfo = getDateRange(input);
             let salesToAnalyze: SaleRecord[] = data.sales;
             let periodText = "كل الأوقات";

             if(dateInfo) {
                 salesToAnalyze = data.sales.filter(s => isWithinInterval(parse(s.date, 'yyyy-MM-dd', new Date()), dateInfo.range));
                 periodText = dateInfo.periodText;
             }

            if (salesToAnalyze.length === 0) {
                 return { text: `لا توجد مبيعات مسجلة ${periodText !== 'كل الأوقات' ? `خلال ${periodText}`: ''} لحساب الأرباح.`, action: null };
            }

            const revenue = salesToAnalyze.reduce((acc, s) => acc + s.totalAmount, 0);
            const cogs = salesToAnalyze.flatMap(s => s.items).reduce((acc, i) => acc + (i.purchasePrice * i.quantity), 0);
            const grossProfit = revenue - cogs;

            return { text: `ملخص الأرباح (${periodText}):\n• إجمالي المبيعات: ${revenue.toLocaleString('ar-IQ')} د.ع\n• تكلفة البضاعة: ${cogs.toLocaleString('ar-IQ')} د.ع\n• صافي الربح: ${grossProfit.toLocaleString('ar-IQ')} د.ع`, action: null };
        }
    },
    {
        keywords: ["آخر الأنشطة", "آخر التعديلات"],
        requiredPermission: { module: 'activityLog', permission: 'read' },
        handler: (input, data) => {
            const recentLogs = data.activityLog.slice(0, 5);
             if (recentLogs.length === 0) {
                return { text: "لا توجد أي أنشطة مسجلة بعد.", action: null };
            }
             const responseText = recentLogs.map(log =>
                `• ${log.user} قام بـ "${log.action}"\n  - التفاصيل: ${log.details}\n  - الوقت: ${format(new Date(log.timestamp), 'h:mm a')}`
            ).join('\n\n');

             return {
                text: `آخر 5 أنشطة في النظام:\n\n${responseText}`,
                action: {
                    type: 'navigate',
                    label: 'عرض السجل الكامل',
                    payload: { url: '/settings/activity-log' }
                }
            };
        }
    },
    {
        keywords: ["شكرا", "شكرًا"],
        handler: () => ({ text: "على الرحب والسعة! أنا هنا للمساعدة في أي وقت.", action: null }),
    },
     {
        keywords: ["منتهية الصلاحية", "اكسباير", "ستنتهي صلاحيتها قريباً", "الكمية المنخفضة"],
        requiredPermission: { module: 'inventory', permission: 'read' },
        handler: (input, data) => {
             if (input.includes("منتهية الصلاحية") || input.includes("اكسباير")) {
                 const expired = data.inventory.filter(item => differenceInDays(parseISO(item.expDate), new Date()) < 0);
                 if (expired.length === 0) {
                     return { text: "لا توجد أي مواد منتهية الصلاحية حاليًا في المخزون.", action: null };
                 }
                 const responseText = expired.slice(0,5).map(item => `• ${item.name} (${item.batchNumber}) - الكمية: ${item.stock}`).join('\n');
                 return {
                     text: `يوجد ${expired.length} مادة منتهية الصلاحية. إليك أحدثها:\n${responseText}`,
                     action: { type: 'navigate', label: 'عرض المخزون', payload: { url: '/inventory' } }
                 };
             }
            if (input.includes("ستنتهي صلاحيتها قريباً")) {
                 const expiringSoon = data.inventory.filter(item => {
                    const daysToExpiry = differenceInDays(parseISO(item.expDate), new Date());
                    return daysToExpiry >= 0 && daysToExpiry <= 60;
                 });
                  if (expiringSoon.length === 0) {
                     return { text: "لا توجد مواد ستنتهي صلاحيتها خلال الـ 60 يومًا القادمة.", action: null };
                 }
                 const responseText = expiringSoon.slice(0,5).map(item => `• ${item.name} (${item.batchNumber}) - تنتهي خلال ${differenceInDays(parseISO(item.expDate), new Date())} يوم`).join('\n');
                 return {
                     text: `يوجد ${expiringSoon.length} مادة ستنتهي صلاحيتها قريباً:\n${responseText}`,
                     action: { type: 'navigate', label: 'عرض المخزون', payload: { url: '/inventory' } }
                 };
            }
            if (input.includes("الكمية المنخفضة")) {
                 const lowStock = data.inventory.filter(item => item.stock > 0 && item.stock < LOW_STOCK_THRESHOLD);
                 if (lowStock.length === 0) {
                     return { text: "لا توجد مواد كميتها منخفضة حاليًا.", action: null };
                 }
                 const responseText = lowStock.slice(0,5).map(item => `• ${item.name} (${item.batchNumber}) - المتبقي: ${item.stock}`).join('\n');
                 return {
                     text: `يوجد ${lowStock.length} مادة كميتها منخفضة:\n${responseText}`,
                      action: { type: 'navigate', label: 'عرض المخزون', payload: { url: '/inventory' } }
                 };
            }
             return null;
        }
    },
    {
        keywords: ["عدد"],
        handler: (input, data) => {
            if (input.includes("العملاء")) {
                return { text: `يوجد حاليًا ${data.customers.length} عميل مسجل في النظام.`, action: null };
            }
             if (input.includes("الموردين")) {
                return { text: `يوجد حاليًا ${data.suppliers.length} مورد مسجل في النظام.`, action: null };
            }
             if (input.includes("المنتجات") || input.includes("المواد")) {
                 const uniqueProducts = new Set(data.inventory.map(i => i.name));
                return { text: `يوجد حاليًا ${uniqueProducts.size} نوع منتج فريد في المخزون.`, action: null };
            }
            return null;
        }
    },
     {
        keywords: ["الأكثر مبيعا", "الاكثر مبيعا"],
        requiredPermission: { module: 'sales', permission: 'read' },
        handler: (input, data) => {
            const dateInfo = getDateRange(input);
            let salesToAnalyze = data.sales;
            let periodText = "على الإطلاق";
             if(dateInfo) {
                 salesToAnalyze = data.sales.filter(s => isWithinInterval(parse(s.date, 'yyyy-MM-dd', new Date()), dateInfo.range));
                 periodText = `خلال ${dateInfo.periodText}`;
             }
             if (salesToAnalyze.length === 0) {
                 return { text: `لا توجد مبيعات ${periodText} لتحليلها.`, action: null };
             }

            const productSales = salesToAnalyze
                .flatMap(s => s.items)
                .reduce((acc, item) => {
                    if (!acc[item.name]) {
                        acc[item.name] = 0;
                    }
                    acc[item.name] += item.quantity;
                    return acc;
                }, {} as Record<string, number>);

            const topSelling = Object.entries(productSales)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5);
            
            if (topSelling.length === 0) {
                 return { text: `لا توجد بيانات مبيعات كافية ${periodText}.`, action: null };
            }

            const responseText = topSelling.map(([name, quantity], i) => `${i + 1}. ${name} - ${quantity} قطعة`).join('\n');
            return {
                text: `المنتجات الأكثر مبيعاً ${periodText}:\n\n${responseText}`,
                action: {
                    type: 'navigate',
                    label: 'عرض تقارير التحليلات',
                    payload: { url: '/reports?tab=analytics' }
                }
            };
        }
    },
     {
        keywords: ["قائمة العملاء"],
        exact: true,
        requiredPermission: { module: 'customers', permission: 'read' },
        handler: (input, data) => {
            if (data.customers.length === 0) {
                return { text: "لا يوجد عملاء مسجلون بعد.", action: null };
            }
            return {
                text: "اختر عميلاً لعرض رصيده:",
                action: {
                    type: 'suggest_options',
                    options: data.customers.map(c => ({
                        label: c.name,
                        query: `رصيد ${c.name}`
                    }))
                }
            };
        }
    },
    {
        keywords: ["قائمة الموردين"],
        exact: true,
        requiredPermission: { module: 'suppliers', permission: 'read' },
        handler: (input, data) => {
            if (data.suppliers.length === 0) {
                return { text: "لا يوجد موردون مسجلون بعد.", action: null };
            }
            return {
                text: "اختر موردًا لعرض رصيده:",
                action: {
                    type: 'suggest_options',
                    options: data.suppliers.map(s => ({
                        label: s.name,
                        query: `رصيد ${s.name}`
                    }))
                }
            };
        }
    },
    {
        keywords: ["قائمة المنتجات"],
        exact: true,
        requiredPermission: { module: 'inventory', permission: 'read' },
        handler: (input, data) => {
            const uniqueProducts = Array.from(new Set(data.inventory.map(item => item.name)));
            if (uniqueProducts.length === 0) {
                return { text: "لا توجد منتجات في المخزون بعد.", action: null };
            }
            return {
                text: "اختر منتجًا لعرض كميته:",
                action: {
                    type: 'suggest_options',
                    options: uniqueProducts.map(name => ({
                        label: name,
                        query: `كمية ${name}`
                    }))
                }
            };
        }
    }
];

const defaultResponse: BotResponse = { 
    text: "عذراً، لم أفهم سؤالك. يمكنك تجربة أحد الخيارات التالية:",
    action: {
        type: 'suggest_options',
        options: [
            { label: 'رصيد الصندوق', query: 'رصيد الصندوق' },
            { label: 'قيمة المخزون', query: 'قيمة المخزون' },
            { label: 'آخر الأنشطة', query: 'آخر الأنشطة' },
        ]
    }
};

const hasPermission = (permissions: RolePermissions, module: Module, permission: Permission): boolean => {
    return permissions[module]?.[permission] ?? false;
};

export function getBotResponse(input: string, data: DataSnapshot, permissions: RolePermissions): BotResponse {
    const trimmedInput = input.trim();

    const accessibleRules = rules.filter(rule => {
        if (!rule.requiredPermission) return true;
        return hasPermission(permissions, rule.requiredPermission.module, rule.requiredPermission.permission);
    });

    const sortedRules = accessibleRules.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    for (const rule of sortedRules) {
        const match = rule.exact 
            ? rule.keywords.some(keyword => trimmedInput === keyword)
            : rule.keywords.some(keyword => trimmedInput.includes(keyword));

        if (match) {
            const response = rule.handler(trimmedInput, data);
            if (response?.action?.type === 'suggest_options') {
                 // Filter suggested options based on permissions
                 response.action.options = response.action.options.filter(opt => {
                    const correspondingRule = rules.find(r => r.keywords.some(k => opt.query.includes(k)));
                    if(!correspondingRule || !correspondingRule.requiredPermission) return true;
                    return hasPermission(permissions, correspondingRule.requiredPermission.module, correspondingRule.requiredPermission.permission);
                 });
            }
            if (response) return response;
        }
    }

    const filteredDefaultOptions = defaultResponse.action?.type === 'suggest_options'
    ? defaultResponse.action.options.filter(opt => {
        const correspondingRule = rules.find(r => r.keywords.some(k => opt.query.includes(k)));
        if (!correspondingRule || !correspondingRule.requiredPermission) return true;
        return hasPermission(permissions, correspondingRule.requiredPermission.module, correspondingRule.requiredPermission.permission);
      })
    : [];

    return {
        ...defaultResponse,
        action: {
            type: 'suggest_options',
            options: filteredDefaultOptions
        }
    };
}
