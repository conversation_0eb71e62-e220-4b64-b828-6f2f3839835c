
"use client";

import { useState, useEffect, useMemo } from "react";
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, PlusCircle, ArrowUpCircle, ArrowDownCircle, Banknote, Printer, Sparkles, Loader2, CheckCircle, HandCoins, Calculator, Minus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
  DialogDescription
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/hooks/use-toast";
import { useStore } from "@/store/erp-store";
import type { CashboxTransaction, SaleRecord, PurchaseRecord, Customer, Supplier, ReturnRecord, ExpenseCategory } from "@/store/erp-store";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Logo } from "@/components/icons";

type NewTransactionState = {
    description: string;
    type: 'income' | 'expense';
    amount: number;
    date: string;
    category: ExpenseCategory;
    partyName: string;
}

const expenseCategories: Record<ExpenseCategory, string> = {
    salaries: 'رواتب',
    rent: 'إيجارات',
    support: 'دعم',
    cash_difference: 'فرق عملة',
    damaged_goods: 'بضاعة تالفة',
    other: 'مصاريف نثرية (أخرى)',
};

function CashboxAnalysis({ transactions }: { transactions: CashboxTransaction[] }) {
    const analysis = useMemo(() => {
        const incomeCount = transactions.filter(t => t.type === 'income').length;
        const expenseCount = transactions.filter(t => t.type === 'expense').length;
        return { incomeCount, expenseCount };
    }, [transactions]);

    return (
        <Card className="mb-8 bg-primary/5">
            <CardHeader className="flex flex-row items-center gap-3">
                <Sparkles className="w-6 h-6 text-primary" />
                <div className="flex-1">
                    <CardTitle className="text-base">تحليل حركات الصندوق</CardTitle>
                </div>
            </CardHeader>
            <CardContent>
                 <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="flex flex-col items-center gap-2">
                        <div className="flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900">
                             <ArrowUpCircle className="h-6 w-6 text-green-600 dark:text-green-300" />
                        </div>
                        <p className="text-sm font-medium">حركات القبض</p>
                        <p className="text-2xl font-bold">{analysis.incomeCount}</p>
                    </div>
                     <div className="flex flex-col items-center gap-2">
                        <div className="flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                             <ArrowDownCircle className="h-6 w-6 text-red-600 dark:text-red-300" />
                        </div>
                        <p className="text-sm font-medium">حركات الصرف</p>
                        <p className="text-2xl font-bold">{analysis.expenseCount}</p>
                    </div>
                     <div className="flex flex-col items-center gap-2">
                        <div className="flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700">
                             <Calculator className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                        </div>
                        <p className="text-sm font-medium">إجمالي الحركات</p>
                        <p className="text-2xl font-bold">{analysis.incomeCount + analysis.expenseCount}</p>
                    </div>
                 </div>
            </CardContent>
        </Card>
    );
}

export default function CashboxPage() {
    const { 
        cashboxTransactions, 
        addCashboxTransaction,
        updateCashboxTransaction,
        deleteCashboxTransaction,
        sales,
        purchases,
        customers,
        suppliers,
        returns,
        settleInvoice,
        addPayment,
        settings,
    } = useStore();

    const [transactionsSearchTerm, setTransactionsSearchTerm] = useState("");
    const [invoicesSearchTerm, setInvoicesSearchTerm] = useState("");
    const { toast } = useToast();

    const initialNewTransactionState: NewTransactionState = {
        description: '',
        type: 'income',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        category: 'other',
        partyName: '',
    };

    const [newTransaction, setNewTransaction] = useState<NewTransactionState>(initialNewTransactionState);
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    
    const [editingTransaction, setEditingTransaction] = useState<CashboxTransaction | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    const [settlementInvoice, setSettlementInvoice] = useState<{ id: string; type: 'sale' | 'purchase'; remainingAmount: number } | null>(null);
    const [settlementAmount, setSettlementAmount] = useState(0);
    const [isSettleDialogOpen, setIsSettleDialogOpen] = useState(false);

    const [paymentParty, setPaymentParty] = useState<{ type: 'customer' | 'supplier', id: number, name: string, balance: number } | null>(null);
    const [paymentAmount, setPaymentAmount] = useState(0);
    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);


    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>, isEditing: boolean = false) => {
        const { name, value } = e.target;
        const setter = isEditing ? setEditingTransaction : setNewTransaction;
        setter(prev => {
            if (!prev) return null;
            return {
                ...prev,
                [name]: name === 'amount' ? Number(value) : value
            }
        });
    };

    const handleTypeChange = (value: 'income' | 'expense', isEditing: boolean = false) => {
        const setter = isEditing ? setEditingTransaction : setNewTransaction;
        setter(prev => (prev ? { ...prev, type: value } : null));
    };

    const handleCategoryChange = (value: ExpenseCategory, isEditing: boolean = false) => {
         const setter = isEditing ? setEditingTransaction : setNewTransaction;
        setter(prev => (prev ? { ...prev, category: value } : null));
    }


    const handleAddNewTransaction = (e: React.FormEvent) => {
        e.preventDefault();
        
        let finalDescription = newTransaction.type === 'expense' 
            ? `${expenseCategories[newTransaction.category]}: ${newTransaction.description}` 
            : newTransaction.description;

        if (newTransaction.partyName) {
            finalDescription = `[${newTransaction.partyName}] ${finalDescription}`;
        }
        
        if (!newTransaction.description || newTransaction.amount <= 0) {
            toast({
                variant: 'destructive',
                title: 'حقول مطلوبة',
                description: 'الرجاء إدخال البيان والمبلغ بشكل صحيح.'
            });
            return;
        }

        const newTx: Omit<CashboxTransaction, 'id'> = {
            date: newTransaction.date,
            type: newTransaction.type,
            amount: newTransaction.amount,
            category: newTransaction.type === 'expense' ? newTransaction.category : undefined,
            partyName: newTransaction.partyName,
            description: finalDescription,
        };

        addCashboxTransaction(newTx);
        setNewTransaction(initialNewTransactionState);
        setIsAddDialogOpen(false);
         toast({ title: 'تمت الإضافة بنجاح', description: `تمت إضافة معاملة جديدة بقيمة ${newTx.amount.toLocaleString('ar-IQ')} د.ع.`});
    };
    
    const openEditDialog = (transaction: CashboxTransaction) => {
        setEditingTransaction({ ...transaction });
        setIsEditDialogOpen(true);
    };

    const handleUpdateTransaction = (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingTransaction) return;

        if (!editingTransaction.description || editingTransaction.amount <= 0) {
            toast({ variant: 'destructive', title: 'حقول مطلوبة', description: 'الرجاء إدخال البيان والمبلغ بشكل صحيح.' });
            return;
        }

        updateCashboxTransaction(editingTransaction.id, editingTransaction);
        setIsEditDialogOpen(false);
        setEditingTransaction(null);
        toast({ title: 'تم التعديل بنجاح', description: 'تم تحديث بيانات المعاملة بنجاح.' });
    };
    
    const handleDeleteTransaction = (transactionId: number) => {
      deleteCashboxTransaction(transactionId);
      toast({ title: 'تم الحذف بنجاح', description: `تم حذف المعاملة وتحديث السجلات المرتبطة بها.` });
    };

    const openSettleDialog = (invoiceId: string, type: 'sale' | 'purchase', remainingAmount: number) => {
        setSettlementInvoice({ id: invoiceId, type, remainingAmount });
        setSettlementAmount(remainingAmount);
        setIsSettleDialogOpen(true);
    };

    const openPaymentDialog = (party: {type: 'customer' | 'supplier', id: number, name: string, balance: number}) => {
        setPaymentParty(party);
        setPaymentAmount(party.balance > 0 ? party.balance : 0);
        setIsPaymentDialogOpen(true);
    };
    
    const handlePrintSettlementReceipt = (
        party: Customer | Supplier, 
        originalInvoiceState: SaleRecord | PurchaseRecord, // The invoice state BEFORE the current settlement
        updatedInvoiceState: SaleRecord | PurchaseRecord, // The invoice state AFTER the current settlement
        settlementAmount: number,
        otherUnpaidInvoices: (SaleRecord | PurchaseRecord)[],
        partyReturns: ReturnRecord[]
    ) => {
        const receiptType = updatedInvoiceState.status === 'paid' ? 'إشعار تسديد فاتورة' : (updatedInvoiceState.id.toString().startsWith('I') ? 'سند قبض' : 'سند صرف');
        const receiptNumber = `RCPT-${Date.now()}`;
        const logoSvg = settings.printLogoSvg ? `data:image/svg+xml;base64,${btoa(settings.printLogoSvg)}` : '';

        // --- Start of Detailed Calculations ---
        const invoiceReturns = returns.filter(r => r.originalInvoiceId === originalInvoiceState.id);
        const totalReturnsAmountForInvoice = invoiceReturns.reduce((acc, r) => acc + r.amount, 0);

        const originalTotalAmount = originalInvoiceState.totalAmount + totalReturnsAmountForInvoice;

        const netInvoiceAmount = originalTotalAmount - totalReturnsAmountForInvoice;
        const previouslyPaid = originalInvoiceState.paidAmount;
        const remainingAfter = netInvoiceAmount - previouslyPaid - settlementAmount;
        // --- End of Detailed Calculations ---

        const otherUnpaidTotal = otherUnpaidInvoices.reduce((acc, inv) => acc + (inv.totalAmount - inv.paidAmount), 0);
        const otherUnpaidInvoicesHtml = otherUnpaidInvoices.length > 0 ? `
            <div class="extra-info-section">
                <h4>فواتير أخرى غير مسددة (${otherUnpaidInvoices.length})</h4>
                <table>
                    <thead><tr><th>رقم الفاتورة</th><th>المبلغ المتبقي</th></tr></thead>
                    <tbody>
                        ${otherUnpaidInvoices.map(inv => `
                            <tr><td>${inv.id}</td><td>${(inv.totalAmount - inv.paidAmount).toLocaleString('ar-IQ')} د.ع</td></tr>
                        `).join('')}
                    </tbody>
                     <tfoot><tr><th>الإجمالي</th><th>${otherUnpaidTotal.toLocaleString('ar-IQ')} د.ع</th></tr></tfoot>
                </table>
            </div>
        ` : '';

        const totalReturnsAmount = partyReturns.reduce((acc, r) => acc + r.amount, 0);
        const returnsHtml = totalReturnsAmount > 0 ? `
             <div class="extra-info-section">
                <h4>ملخص المرتجعات (لكامل الحساب)</h4>
                <table>
                    <tbody>
                        <tr><td>إجمالي قيمة المرتجعات المسجلة</td><td>${totalReturnsAmount.toLocaleString('ar-IQ')} د.ع</td></tr>
                    </tbody>
                </table>
            </div>
        `: '';
        
        const receiptWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (receiptWindow) {
            receiptWindow.document.write(`
                <html>
                    <head>
                        <title>${receiptType}</title>
                        <style>
                            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                            body { font-family: 'Tajawal', sans-serif; direction: rtl; margin: 0; background-color: #f8f9fa; }
                            .page { max-width: 800px; margin: 2rem auto; background: white; padding: 2rem; box-shadow: 0 0 15px rgba(0,0,0,0.1); }
                            .header { display: flex; justify-content: space-between; align-items: flex-start; padding-bottom: 1rem; border-bottom: 2px solid #333; }
                            .header .company-info { text-align: right; }
                            .header .company-info h1 { margin: 0; font-size: 1.8rem; color: #333; }
                            .header .company-info p { margin: 0.2rem 0; font-size: 0.9rem; color: #555; }
                            .header .logo { width: 100px; height: 100px; object-fit: contain; }
                            .receipt-title { text-align: center; margin: 1.5rem 0; }
                            .receipt-title h2 { margin: 0; font-size: 1.5rem; color: ${originalInvoiceState.id.toString().startsWith('I') ? '#28a745' : '#dc3545'}; border: 2px solid; padding: 0.5rem 1rem; display: inline-block; border-radius: 8px; }
                            .details { margin-bottom: 2rem; font-size: 1rem; line-height: 1.8; }
                            .details .row { display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px dashed #ccc; }
                            .details .row strong { color: #333; }
                            .financial-table { width: 100%; border-collapse: collapse; margin-top: 1.5rem; font-size: 1rem; }
                            .financial-table th, .financial-table td { border: 1px solid #dee2e6; padding: 10px; text-align: right; }
                            .financial-table thead { background-color: #e9ecef; }
                            .financial-table tbody tr:nth-child(even) { background-color: #f8f9fa; }
                            .total-row td { font-weight: bold; font-size: 1.1rem; }
                            .extra-info-section { margin-top: 1.5rem; border-top: 1px solid #ccc; padding-top: 1rem; }
                            .extra-info-section h4 { font-size: 1rem; margin-bottom: 0.5rem; }
                            .extra-info-section table { width: 100%; font-size: 0.9rem; border-collapse: collapse; }
                            .extra-info-section th, .extra-info-section td { border: 1px solid #eee; padding: 6px; text-align: right; }
                            .extra-info-section thead { background-color: #f8f8f8; font-weight: bold; }
                             .extra-info-section tfoot { background-color: #f1f1f1; font-weight: bold; }
                            .footer { display:flex; justify-content:space-around; margin-top: 3rem; padding-top: 1rem; border-top: 1px solid #ccc; }
                            .footer div { text-align: center; font-size: 0.9rem; }
                            @media print { 
                                body { background-color: #fff; font-size: 10pt; }
                                .page { margin: 0; box-shadow: none; padding: 1cm; } 
                                .header .company-info h1 {font-size: 1.5rem;}
                                .receipt-title h2 {font-size: 1.3rem;}
                            }
                        </style>
                    </head>
                    <body>
                        <div class="page">
                            <div class="header">
                                <div class="company-info">
                                    <h1>${settings.companyInfo.name}</h1>
                                    <p>${settings.companyInfo.description}</p>
                                    <p>${settings.companyInfo.address}</p>
                                </div>
                                <div class="logo-container">
                                    ${logoSvg ? `<img src="${logoSvg}" alt="Logo" class="logo" />` : `<div class="logo"></div>`}
                                </div>
                            </div>
                            <div class="receipt-title"><h2>${receiptType}</h2></div>
                            <div class="details">
                                <div class="row"><span><strong>رقم الإيصال:</strong></span><span>${receiptNumber}</span></div>
                                <div class="row"><span><strong>التاريخ:</strong></span><span>${new Date().toLocaleDateString('ar-IQ')}</span></div>
                                <div class="row"><span><strong>تم استلام مبلغ من / صرف مبلغ إلى السيد/ة:</strong></span><span>${party.name}</span></div>
                                <div class="row"><span><strong>وذلك عن تسديد دفعة من / إلى الفاتورة رقم:</strong></span><span>${originalInvoiceState.id}</span></div>
                            </div>
                            <table class="financial-table">
                                <thead><tr><th>البيان</th><th>المبلغ (د.ع)</th></tr></thead>
                                <tbody>
                                    <tr><td>إجمالي الفاتورة الأصلي</td><td>${originalTotalAmount.toLocaleString('ar-IQ')}</td></tr>
                                    <tr><td>إجمالي المرتجعات من هذه الفاتورة</td><td style="color: #dc3545;">${totalReturnsAmountForInvoice > 0 ? `(${totalReturnsAmountForInvoice.toLocaleString('ar-IQ')})` : '0'}</td></tr>
                                    <tr><td>صافي الفاتورة بعد المرتجعات</td><td>${netInvoiceAmount.toLocaleString('ar-IQ')}</td></tr>
                                    <tr><td>المدفوع سابقاً</td><td style="color: #6c757d;">(${previouslyPaid.toLocaleString('ar-IQ')})</td></tr>
                                    <tr style="background-color: #e9f7ef;"><td style="font-weight: bold;">الدفعة الحالية</td><td style="color: ${originalInvoiceState.id.toString().startsWith('I') ? '#28a745' : '#dc3545'}; font-weight: bold;">${settlementAmount.toLocaleString('ar-IQ')}</td></tr>
                                    <tr class="total-row" style="background-color: #f8d7da; color: #721c24;"><td>المتبقي من هذه الفاتورة</td><td>${remainingAfter.toLocaleString('ar-IQ')}</td></tr>
                                </tbody>
                            </table>
                            ${returnsHtml}
                            ${otherUnpaidInvoicesHtml}
                            <div class="footer">
                                <div><p>____________________</p><p>توقيع المستلم</p></div>
                                <div><p>____________________</p><p>توقيع المحاسب</p></div>
                            </div>
                        </div>
                        <script>setTimeout(()=>{window.print();window.close();}, 500);</script>
                    </body>
                </html>
            `);
            receiptWindow.document.close();
        }
    }

    const handleSettleInvoice = (e: React.FormEvent) => {
        e.preventDefault();
        if (!settlementInvoice || settlementAmount <= 0 || settlementAmount > settlementInvoice.remainingAmount) {
             toast({
                variant: 'destructive',
                title: "مبلغ غير صحيح",
                description: `المبلغ المدفوع يجب أن يكون أكبر من صفر وأقل أو يساوي المبلغ المتبقي.`,
            });
            return;
        }

        let party: Customer | Supplier | undefined;
        let invoiceBeforeSettlement: SaleRecord | PurchaseRecord | undefined;
        
        if (settlementInvoice.type === 'sale') {
            invoiceBeforeSettlement = sales.find(s => s.id === settlementInvoice.id);
            if (invoiceBeforeSettlement) {
                 party = customers.find(c => c.id === invoiceBeforeSettlement?.customerId);
            }
        } else {
            invoiceBeforeSettlement = purchases.find(p => p.id === settlementInvoice.id);
            if(invoiceBeforeSettlement) {
                party = suppliers.find(s => s.id === invoiceBeforeSettlement?.supplierId);
            }
        }

        if (!party || !invoiceBeforeSettlement) return;
        
        const originalInvoiceState = JSON.parse(JSON.stringify(invoiceBeforeSettlement));
        
        settleInvoice(settlementInvoice.id, settlementInvoice.type, settlementAmount);

        setTimeout(() => {
            const allSales = useStore.getState().sales;
            const allPurchases = useStore.getState().purchases;
            const allReturns = useStore.getState().returns;
            
            const updatedInvoiceState = (settlementInvoice.type === 'sale' 
                ? allSales.find(s => s.id === settlementInvoice!.id) 
                : allPurchases.find(p => p.id === settlementInvoice!.id))!;

            let otherUnpaidInvoices: (SaleRecord | PurchaseRecord)[] = [];
            let partyReturns: ReturnRecord[] = [];

            if (settlementInvoice.type === 'sale') {
                otherUnpaidInvoices = allSales.filter(s => s.customerId === party!.id && s.id !== updatedInvoiceState!.id && (s.status === 'unpaid' || s.status === 'partial'));
                partyReturns = allReturns.filter(r => r.type === 'sales' && r.customerOrSupplierId === party!.id);
            } else {
                otherUnpaidInvoices = allPurchases.filter(p => p.supplierId === party!.id && p.id !== updatedInvoiceState!.id && (p.status === 'unpaid' || p.status === 'partial'));
                partyReturns = allReturns.filter(r => r.type === 'purchase' && r.customerOrSupplierId === party!.id);
            }

            toast({
                title: "تم تسجيل الدفعة",
                description: `تم تسجيل دفعة بقيمة ${settlementAmount.toLocaleString()} وتحديث حالة الفاتورة.`,
            });
            
            if (updatedInvoiceState) {
                handlePrintSettlementReceipt(party!, originalInvoiceState, updatedInvoiceState, settlementAmount, otherUnpaidInvoices, partyReturns);
            }

            setIsSettleDialogOpen(false);
            setSettlementInvoice(null);
            setSettlementAmount(0);
        }, 100); 
    };

    const handlePaymentSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!paymentParty || paymentAmount <= 0) {
            toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء إدخال مبلغ صحيح.' });
            return;
        }

        addPayment({
            partyType: paymentParty.type,
            partyId: paymentParty.id,
            partyName: paymentParty.name,
            amount: paymentAmount,
        });

        toast({
            title: 'تم تسجيل الدفعة',
            description: `تم تسجيل دفعة بقيمة ${paymentAmount.toLocaleString()} د.ع لـ ${paymentParty.name}. تم تحديث صندوق النقد والفواتير.`
        });

        setIsPaymentDialogOpen(false);
        setPaymentParty(null);
    };

    const handlePrint = () => { window.print(); }

    const handlePrintReceipt = (transaction: CashboxTransaction) => {
        const receiptType = transaction.type === 'income' ? 'سند قبض' : 'سند صرف';
        const receiptNumber = `TXN-${transaction.id}`;
        const receiptWindow = window.open('', '_blank', 'width=800,height=600');
        const logoSvg = settings.printLogoSvg ? `data:image/svg+xml;base64,${btoa(settings.printLogoSvg)}` : '';

        if (receiptWindow) {
            receiptWindow.document.write(`
                <html>
                    <head>
                        <title>${receiptType}</title>
                        <style>
                           @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                            body { font-family: 'Tajawal', sans-serif; direction: rtl; margin: 0; background-color: #f8f9fa; }
                            .page { max-width: 800px; margin: 2rem auto; background: white; padding: 2rem; box-shadow: 0 0 15px rgba(0,0,0,0.1); }
                            .header { display: flex; justify-content: space-between; align-items: flex-start; padding-bottom: 1rem; border-bottom: 2px solid #333; }
                            .header .company-info { text-align: right; }
                            .header .company-info h1 { margin: 0; font-size: 1.8rem; color: #333; }
                            .header .company-info p { margin: 0.2rem 0; font-size: 0.9rem; color: #555; }
                            .header .logo { width: 100px; height: 100px; object-fit: contain; }
                            .receipt-title { text-align: center; margin: 1.5rem 0; }
                            .receipt-title h2 { margin: 0; font-size: 1.5rem; color: ${transaction.type === 'income' ? '#28a745' : '#dc3545'}; border: 2px solid; padding: 0.5rem 1rem; display: inline-block; border-radius: 8px; }
                            .details { margin-bottom: 2rem; font-size: 1.1rem; line-height: 1.8; }
                            .details .row { display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px dashed #ccc; }
                            .details .row strong { color: #333; }
                            .amount-box { text-align: center; margin: 2rem 0; }
                            .amount-box .label { font-size: 1.1rem; color: #6c757d; }
                            .amount-box .amount { font-size: 2.2rem; font-weight: bold; color: #333; }
                            .footer { display:flex; justify-content:space-around; margin-top: 4rem; padding-top: 1.5rem; border-top: 1px solid #ccc; }
                            .footer div { text-align: center; font-size: 1rem; }
                            @media print { 
                                body { background-color: #fff; }
                                .page { margin: 0; box-shadow: none; } 
                            }
                        </style>
                    </head>
                    <body>
                         <div class="page">
                            <div class="header">
                                <div class="company-info">
                                    <h1>${settings.companyInfo.name}</h1>
                                    <p>${settings.companyInfo.description}</p>
                                    <p>${settings.companyInfo.address}</p>
                                </div>
                                <div class="logo-container">
                                    ${logoSvg ? `<img src="${logoSvg}" alt="Logo" class="logo" />` : `<div class="logo"></div>`}
                                </div>
                            </div>
                            <div class="receipt-title"><h2>${receiptType}</h2></div>
                            <div class="details">
                                <div class="row"><span><strong>رقم الإيصال:</strong></span><span>${receiptNumber}</span></div>
                                <div class="row"><span><strong>التاريخ:</strong></span><span>${new Date(transaction.date).toLocaleDateString('ar-IQ')}</span></div>
                                <div class="row"><span><strong>البيان:</strong></span><span>${transaction.description}</span></div>
                            </div>
                            <div class="amount-box">
                                <div class="label">المبلغ</div>
                                <div class="amount" style="color: ${transaction.type === 'income' ? '#28a745' : '#dc3545'};">${transaction.amount.toLocaleString('ar-IQ')} د.ع</div>
                            </div>
                            <div class="footer">
                                <div><p>____________________</p><p>توقيع المستلم</p></div>
                                <div><p>____________________</p><p>توقيع المحاسب</p></div>
                            </div>
                        </div>
                        <script>setTimeout(()=>{window.print();window.close();}, 500);</script>
                    </body>
                </html>
            `);
            receiptWindow.document.close();
        }
    }
    
    const filteredTransactions = cashboxTransactions.filter(t => 
        (t.description.toLowerCase().includes(transactionsSearchTerm.toLowerCase())) ||
        (t.partyName && t.partyName.toLowerCase().includes(transactionsSearchTerm.toLowerCase()))
    );

    const searchSummary = useMemo(() => {
        if (!transactionsSearchTerm) {
            return null;
        }

        const income = filteredTransactions
            .filter(t => t.type === 'income')
            .reduce((sum, t) => sum + t.amount, 0);

        const expense = filteredTransactions
            .filter(t => t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0);

        return { income, expense };
    }, [filteredTransactions, transactionsSearchTerm]);
    
    const unpaidSales = sales.filter(s => (s.status === 'unpaid' || s.status === 'partial') && (s.id.toLowerCase().includes(invoicesSearchTerm.toLowerCase()) || s.customerName.toLowerCase().includes(invoicesSearchTerm.toLowerCase())));
    const unpaidPurchases = purchases.filter(p => (p.status === 'unpaid' || p.status === 'partial') && (p.id.toLowerCase().includes(invoicesSearchTerm.toLowerCase()) || p.supplierName.toLowerCase().includes(invoicesSearchTerm.toLowerCase())));
    
    const paidSales = sales.filter(s => s.status === 'paid' && (s.id.toLowerCase().includes(invoicesSearchTerm.toLowerCase()) || s.customerName.toLowerCase().includes(invoicesSearchTerm.toLowerCase())));
    const paidPurchases = purchases.filter(p => p.status === 'paid' && (s.id.toLowerCase().includes(invoicesSearchTerm.toLowerCase()) || s.supplierName.toLowerCase().includes(invoicesSearchTerm.toLowerCase())));

    // Group unpaid invoices by customer/supplier
    const unpaidSalesByCustomer = unpaidSales.reduce((acc, inv) => {
        if (!acc[inv.customerId]) {
            acc[inv.customerId] = {
                customerName: inv.customerName,
                totalDebt: 0,
                invoices: [],
            };
        }
        acc[inv.customerId].invoices.push(inv);
        acc[inv.customerId].totalDebt += inv.totalAmount - inv.paidAmount;
        return acc;
    }, {} as Record<string, { customerName: string; totalDebt: number; invoices: SaleRecord[] }>);

    const unpaidPurchasesBySupplier = unpaidPurchases.reduce((acc, inv) => {
        if (!acc[inv.supplierId]) {
            acc[inv.supplierId] = {
                supplierName: inv.supplierName,
                totalCredit: 0,
                invoices: [],
            };
        }
        acc[inv.supplierId].invoices.push(inv);
        acc[inv.supplierId].totalCredit += inv.totalAmount - inv.paidAmount;
        return acc;
    }, {} as Record<string, { supplierName: string; totalCredit: number; invoices: PurchaseRecord[] }>);

    const totalIncome = cashboxTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
    const totalExpense = cashboxTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
    const currentBalance = totalIncome - totalExpense;

  return (
    <>
      <PageHeader
        title="صندوق النقد"
        description="تسجيل ومتابعة المعاملات اليومية والديون."
        action={
            <div className="flex gap-2 no-print">
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                    <DialogTrigger asChild><Button><PlusCircle className="ml-2 h-4 w-4" />إضافة معاملة يدوية</Button></DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader><DialogTitle>إضافة معاملة جديدة</DialogTitle></DialogHeader>
                        <form onSubmit={handleAddNewTransaction}><div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="date" className="text-right">التاريخ</Label><Input id="date" name="date" type="date" value={newTransaction.date} onChange={(e) => handleInputChange(e)} className="col-span-3" /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label className="text-right">النوع</Label><RadioGroup className="col-span-3 flex gap-4" onValueChange={(value: 'income' | 'expense') => handleTypeChange(value)} value={newTransaction.type}><div className="flex items-center space-x-2 space-x-reverse"><RadioGroupItem value="income" id="r-income" /><Label htmlFor="r-income">قبض</Label></div><div className="flex items-center space-x-2 space-x-reverse"><RadioGroupItem value="expense" id="r-expense" /><Label htmlFor="r-expense">صرف</Label></div></RadioGroup></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="partyName" className="text-right">الشخص/الجهة</Label><Input id="partyName" name="partyName" value={newTransaction.partyName} onChange={(e) => handleInputChange(e)} className="col-span-3" placeholder="اختياري (مثال: أحمد)" /></div>
                            {newTransaction.type === 'expense' && (
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="category" className="text-right">فئة المصروف</Label>
                                    <div className="col-span-3">
                                    <Select value={newTransaction.category} onValueChange={(value: ExpenseCategory) => handleCategoryChange(value)}>
                                        <SelectTrigger><SelectValue/></SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(expenseCategories).map(([key, value]) => (
                                                <SelectItem key={key} value={key}>{value}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    </div>
                                </div>
                            )}
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="description" className="text-right">البيان</Label><Input id="description" name="description" value={newTransaction.description} onChange={(e) => handleInputChange(e)} className="col-span-3" placeholder={newTransaction.type === 'expense' ? 'مثال: فاتورة كهرباء' : 'مثال: دفعة من عميل'} /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="amount" className="text-right">المبلغ</Label><Input id="amount" name="amount" type="number" value={newTransaction.amount || ''} onChange={(e) => handleInputChange(e)} className="col-span-3" placeholder="0" /></div>
                        </div><DialogFooter><DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose><Button type="submit">حفظ</Button></DialogFooter></form>
                    </DialogContent>
                </Dialog>
                 <Button variant="outline" onClick={handlePrint}><Printer className="ml-2 h-4 w-4" />طباعة</Button>
            </div>
        }
      />
      <div className="print-area">
        <CashboxAnalysis transactions={cashboxTransactions} />

        <div className="grid gap-4 md:grid-cols-3 mb-8">
            <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">إجمالي المقبوضات</CardTitle><ArrowUpCircle className="h-4 w-4 text-green-500" /></CardHeader><CardContent><div className="text-2xl font-bold text-green-600">{totalIncome.toLocaleString('ar-IQ')} د.ع</div><p className="text-xs text-muted-foreground">مجموع المبالغ التي تم قبضها</p></CardContent></Card>
            <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">إجمالي المدفوعات</CardTitle><ArrowDownCircle className="h-4 w-4 text-red-500" /></CardHeader><CardContent><div className="text-2xl font-bold text-red-600">{totalExpense.toLocaleString('ar-IQ')} د.ع</div><p className="text-xs text-muted-foreground">مجموع المبالغ التي تم صرفها</p></CardContent></Card>
            <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">الرصيد الحالي</CardTitle><Banknote className="h-4 w-4 text-muted-foreground" /></CardHeader><CardContent><div className="text-2xl font-bold">{currentBalance.toLocaleString('ar-IQ')} د.ع</div><p className="text-xs text-muted-foreground">الرصيد المتاح في الصندوق</p></CardContent></Card>
        </div>

        <Tabs defaultValue="transactions" className="w-full no-print">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="transactions">سجل المعاملات</TabsTrigger>
            <TabsTrigger value="unpaid">الفواتير غير المسددة</TabsTrigger>
            <TabsTrigger value="paid">الفواتير المسددة</TabsTrigger>
          </TabsList>
          <TabsContent value="transactions">
            <Card>
                <CardHeader>
                <CardTitle>سجل المعاملات</CardTitle>
                <CardDescription>عرض وتصفية المعاملات المسجلة في الصندوق.</CardDescription>
                <div className="pt-4"><Input placeholder="بحث بالبيان أو اسم الشخص..." value={transactionsSearchTerm} onChange={(e) => setTransactionsSearchTerm(e.target.value)} /></div>
                </CardHeader>
                <CardContent>
                {searchSummary && (
                    <Card className="my-4 bg-muted/50">
                        <CardHeader>
                            <CardTitle className="text-base">ملخص البحث عن "{transactionsSearchTerm}"</CardTitle>
                        </CardHeader>
                        <CardContent className="grid grid-cols-2 gap-4">
                            <div>
                                <p className="text-sm text-muted-foreground">مجموع المقبوضات للبحث</p>
                                <p className="text-lg font-bold text-green-600">{searchSummary.income.toLocaleString('ar-IQ')} د.ع</p>
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">مجموع المصروفات للبحث</p>
                                <p className="text-lg font-bold text-red-600">{searchSummary.expense.toLocaleString('ar-IQ')} د.ع</p>
                            </div>
                        </CardContent>
                    </Card>
                )}
                <Table>
                    <TableHeader><TableRow><TableHead>التاريخ</TableHead><TableHead>البيان</TableHead><TableHead>النوع</TableHead><TableHead>المبلغ</TableHead><TableHead className="text-right">إجراءات</TableHead></TableRow></TableHeader>
                    <TableBody>
                    {filteredTransactions.length === 0 && (
                        <TableRow><TableCell colSpan={5} className="text-center h-24">لا توجد نتائج مطابقة للبحث.</TableCell></TableRow>
                    )}
                    {filteredTransactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                        <TableCell>{transaction.date}</TableCell>
                        <TableCell className="font-medium">{transaction.description}</TableCell>
                        <TableCell><Badge variant={transaction.type === 'income' ? 'secondary' : 'destructive'}>{transaction.type === 'income' ? 'قبض' : 'صرف'}</Badge></TableCell>
                        <TableCell className={`${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'} font-semibold`}>{transaction.amount.toLocaleString('ar-IQ')} د.ع</TableCell>
                        <TableCell className="text-right">
                        <AlertDialog>
                                <DropdownMenu>
                                <DropdownMenuTrigger asChild><Button variant="ghost" className="h-8 w-8 p-0"><span className="sr-only">فتح القائمة</span><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => handlePrintReceipt(transaction)}>طباعة السند</DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => openEditDialog(transaction)}>تعديل المعاملة</DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <AlertDialogTrigger asChild><DropdownMenuItem className="text-destructive">حذف المعاملة</DropdownMenuItem></AlertDialogTrigger>
                                </DropdownMenuContent>
                                </DropdownMenu>
                                <AlertDialogContent>
                                    <AlertDialogHeader><AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle><AlertDialogDescription>هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المعاملة بشكل دائم من سجلاتك وسيتم عكس تأثيرها على الفواتير والحسابات المرتبطة بها إن وجدت.</AlertDialogDescription></AlertDialogHeader>
                                    <AlertDialogFooter><AlertDialogCancel>إلغاء</AlertDialogCancel><AlertDialogAction onClick={() => handleDeleteTransaction(transaction.id)}>متابعة</AlertDialogAction></AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </TableCell>
                        </TableRow>
                    ))}
                    </TableBody>
                </Table>
                </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="unpaid">
             <Card>
                <CardHeader>
                    <CardTitle>الفواتير غير المسددة</CardTitle>
                    <CardDescription>عرض الفواتير التي لم يتم تسديدها بالكامل.</CardDescription>
                     <div className="pt-4"><Input placeholder="بحث باسم العميل/المورد أو رقم الفاتورة..." value={invoicesSearchTerm} onChange={(e) => setInvoicesSearchTerm(e.target.value)} /></div>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div>
                        <h3 className="text-lg font-semibold mb-2">مبيعات (ديون العملاء)</h3>
                        {Object.keys(unpaidSalesByCustomer).length === 0 && <p className="text-sm text-muted-foreground text-center py-4">لا توجد ديون حالية على العملاء.</p>}
                        {Object.entries(unpaidSalesByCustomer).map(([customerId, data]) => (
                            <Card key={customerId} className="mb-4">
                                <CardHeader className="flex flex-row items-center justify-between p-4 bg-muted/50">
                                    <div className="font-bold">{data.customerName} (إجمالي الدين: {data.totalDebt.toLocaleString('ar-IQ')} د.ع)</div>
                                    <Button size="sm" onClick={() => openPaymentDialog({type: 'customer', id: Number(customerId), name: data.customerName, balance: data.totalDebt})}>
                                        <HandCoins className="ml-2 h-4 w-4"/>تسديد دفعة شاملة
                                    </Button>
                                </CardHeader>
                                <CardContent className="p-0">
                                     <Table>
                                        <TableHeader><TableRow><TableHead>رقم الفاتورة</TableHead><TableHead>التاريخ</TableHead><TableHead>الحالة</TableHead><TableHead>المتبقي</TableHead><TableHead className="text-right">إجراء</TableHead></TableRow></TableHeader>
                                        <TableBody>
                                        {data.invoices.map(invoice => {
                                            const remainingAmount = invoice.totalAmount - invoice.paidAmount;
                                            return (
                                                <TableRow key={invoice.id}>
                                                    <TableCell>{invoice.id}</TableCell>
                                                    <TableCell>{invoice.date}</TableCell>
                                                    <TableCell><Badge variant={invoice.status === 'partial' ? 'secondary' : 'destructive'}>{invoice.status === 'partial' ? 'جزئي' : 'غير مسدد'}</Badge></TableCell>
                                                    <TableCell>{remainingAmount.toLocaleString()} د.ع</TableCell>
                                                    <TableCell className="text-right"><Button size="sm" variant="outline" onClick={() => openSettleDialog(invoice.id, 'sale', remainingAmount)}>تسديد</Button></TableCell>
                                                </TableRow>
                                            )
                                        })}
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold mb-2">مشتريات (ديون للموردين)</h3>
                         {Object.keys(unpaidPurchasesBySupplier).length === 0 && <p className="text-sm text-muted-foreground text-center py-4">لا توجد ديون حالية للموردين.</p>}
                         {Object.entries(unpaidPurchasesBySupplier).map(([supplierId, data]) => (
                            <Card key={supplierId} className="mb-4">
                                <CardHeader className="flex flex-row items-center justify-between p-4 bg-muted/50">
                                    <div className="font-bold">{data.supplierName} (إجمالي الدين: {data.totalCredit.toLocaleString('ar-IQ')} د.ع)</div>
                                     <Button size="sm" onClick={() => openPaymentDialog({type: 'supplier', id: Number(supplierId), name: data.supplierName, balance: data.totalCredit})}>
                                        <HandCoins className="ml-2 h-4 w-4"/>تسديد دفعة شاملة
                                    </Button>
                                </CardHeader>
                                <CardContent className="p-0">
                                     <Table>
                                        <TableHeader><TableRow><TableHead>رقم الفاتورة</TableHead><TableHead>التاريخ</TableHead><TableHead>الحالة</TableHead><TableHead>المتبقي</TableHead><TableHead className="text-right">إجراء</TableHead></TableRow></TableHeader>
                                        <TableBody>
                                        {data.invoices.map(invoice => {
                                            const remainingAmount = invoice.totalAmount - invoice.paidAmount;
                                            return (
                                                <TableRow key={invoice.id}>
                                                    <TableCell>{invoice.id}</TableCell>
                                                    <TableCell>{invoice.date}</TableCell>
                                                    <TableCell><Badge variant={invoice.status === 'partial' ? 'secondary' : 'destructive'}>{invoice.status === 'partial' ? 'جزئي' : 'غير مسدد'}</Badge></TableCell>
                                                    <TableCell>{remainingAmount.toLocaleString()} د.ع</TableCell>
                                                    <TableCell className="text-right"><Button size="sm" variant="outline" onClick={() => openSettleDialog(invoice.id, 'purchase', remainingAmount)}>تسديد</Button></TableCell>
                                                </TableRow>
                                            )
                                        })}
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>
          </TabsContent>
           <TabsContent value="paid">
            <Card>
                <CardHeader>
                    <CardTitle>الفواتير المسددة بالكامل</CardTitle>
                    <CardDescription>عرض الفواتير التي تم تسديدها بالكامل.</CardDescription>
                     <div className="pt-4"><Input placeholder="بحث باسم العميل/المورد أو رقم الفاتورة..." value={invoicesSearchTerm} onChange={(e) => setInvoicesSearchTerm(e.target.value)} /></div>
                </CardHeader>
                <CardContent className="grid gap-6">
                    <div>
                        <h3 className="text-lg font-semibold mb-2">مبيعات مسددة</h3>
                        <Table>
                            <TableHeader><TableRow><TableHead>رقم الفاتورة</TableHead><TableHead>العميل</TableHead><TableHead>التاريخ</TableHead><TableHead>المبلغ</TableHead><TableHead className="text-right">الحالة</TableHead></TableRow></TableHeader>
                            <TableBody>
                                {paidSales.length === 0 && <TableRow><TableCell colSpan={5} className="text-center h-24">لا توجد فواتير مبيعات مسددة.</TableCell></TableRow>}
                                {paidSales.map(invoice => (
                                    <TableRow key={invoice.id}>
                                        <TableCell>{invoice.id}</TableCell>
                                        <TableCell>{invoice.customerName}</TableCell>
                                        <TableCell>{invoice.date}</TableCell>
                                        <TableCell>{invoice.totalAmount.toLocaleString()} د.ع</TableCell>
                                        <TableCell className="text-right">
                                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                <CheckCircle className="h-3 w-3 ml-1" />
                                                مسددة
                                            </Badge>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                     <div>
                        <h3 className="text-lg font-semibold mb-2">مشتريات مسددة</h3>
                         <Table>
                            <TableHeader><TableRow><TableHead>رقم الفاتورة</TableHead><TableHead>المورد</TableHead><TableHead>التاريخ</TableHead><TableHead>المبلغ</TableHead><TableHead className="text-right">الحالة</TableHead></TableRow></TableHeader>
                            <TableBody>
                                {paidPurchases.length === 0 && <TableRow><TableCell colSpan={5} className="text-center h-24">لا توجد فواتير مشتريات مسددة.</TableCell></TableRow>}
                                {paidPurchases.map(invoice => (
                                    <TableRow key={invoice.id}>
                                        <TableCell>{invoice.id}</TableCell>
                                        <TableCell>{invoice.supplierName}</TableCell>
                                        <TableCell>{invoice.date}</TableCell>
                                        <TableCell>{invoice.totalAmount.toLocaleString()} د.ع</TableCell>
                                        <TableCell className="text-right">
                                             <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                <CheckCircle className="h-3 w-3 ml-1" />
                                                مسددة
                                            </Badge>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

       {/* Edit Transaction Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader><DialogTitle>تعديل المعاملة</DialogTitle></DialogHeader>
                {editingTransaction && (
                    <form onSubmit={handleUpdateTransaction}>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="edit-date" className="text-right">التاريخ</Label><Input id="edit-date" name="date" type="date" value={editingTransaction.date} onChange={(e) => handleInputChange(e, true)} className="col-span-3" /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="edit-partyName" className="text-right">الشخص/الجهة</Label><Input id="edit-partyName" name="partyName" value={editingTransaction.partyName || ''} onChange={(e) => handleInputChange(e, true)} className="col-span-3" /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="edit-description" className="text-right">البيان</Label><Input id="edit-description" name="description" value={editingTransaction.description} onChange={(e) => handleInputChange(e, true)} className="col-span-3" /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label htmlFor="edit-amount" className="text-right">المبلغ</Label><Input id="edit-amount" name="amount" type="number" value={editingTransaction.amount || ''} onChange={(e) => handleInputChange(e, true)} className="col-span-3" /></div>
                            <div className="grid grid-cols-4 items-center gap-4"><Label className="text-right">النوع</Label><RadioGroup className="col-span-3 flex gap-4" onValueChange={(value: 'income' | 'expense') => handleTypeChange(value, true)} value={editingTransaction.type}><div className="flex items-center space-x-2 space-x-reverse"><RadioGroupItem value="income" id="edit-r-income" /><Label htmlFor="edit-r-income">قبض</Label></div><div className="flex items-center space-x-2 space-x-reverse"><RadioGroupItem value="expense" id="edit-r-expense" /><Label htmlFor="edit-r-expense">صرف</Label></div></RadioGroup></div>
                        </div>
                        <DialogFooter><Button type="button" variant="secondary" onClick={() => setIsEditDialogOpen(false)}>إلغاء</Button><Button type="submit">حفظ التغييرات</Button></DialogFooter>
                    </form>
                )}
            </DialogContent>
        </Dialog>

        {/* Settle Single Invoice Dialog */}
        <Dialog open={isSettleDialogOpen} onOpenChange={setIsSettleDialogOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>تسجيل دفعة لفاتورة</DialogTitle>
                    <DialogDescription>
                        أدخل المبلغ الذي تم دفعه. يمكنك تسديد جزء من المبلغ أو كله.
                    </DialogDescription>
                </DialogHeader>
                {settlementInvoice && (
                    <form onSubmit={handleSettleInvoice}>
                        <div className="grid gap-4 py-4">
                            <p>المبلغ المتبقي على الفاتورة: <span className="font-bold">{settlementInvoice.remainingAmount.toLocaleString('ar-IQ')} د.ع</span></p>
                            <div className="space-y-2">
                                <Label htmlFor="settlement-amount">المبلغ المدفوع</Label>
                                <Input
                                    id="settlement-amount"
                                    type="number"
                                    value={settlementAmount || ''}
                                    onChange={(e) => setSettlementAmount(Number(e.target.value))}
                                    placeholder="أدخل المبلغ"
                                    required
                                    max={settlementInvoice.remainingAmount}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
                            <Button type="submit">حفظ الدفعة</Button>
                        </DialogFooter>
                    </form>
                )}
            </DialogContent>
        </Dialog>

         {/* Settle Bulk Payment Dialog */}
        <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>تسجيل دفعة شاملة</DialogTitle>
                     <DialogDescription>
                       سيتم توزيع المبلغ على أقدم الفواتير غير المسددة أولاً.
                    </DialogDescription>
                </DialogHeader>
                {paymentParty && (
                    <form onSubmit={handlePaymentSubmit}>
                        <div className="grid gap-4 py-4">
                            <p>تسجيل دفعة لـ: <span className="font-bold">{paymentParty.name}</span></p>
                             <p>إجمالي الدين الحالي: <span className="font-bold">{paymentParty.balance.toLocaleString('ar-IQ')} د.ع</span></p>
                            <div className="space-y-2">
                                <Label htmlFor="payment-amount">مبلغ الدفعة</Label>
                                <Input
                                    id="payment-amount"
                                    type="number"
                                    value={paymentAmount || ''}
                                    onChange={(e) => setPaymentAmount(Number(e.target.value))}
                                    placeholder="أدخل المبلغ"
                                    required
                                    max={paymentParty.balance > 0 ? paymentParty.balance : undefined}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
                            <Button type="submit">حفظ الدفعة</Button>
                        </DialogFooter>
                    </form>
                )}
            </DialogContent>
        </Dialog>
    </>
  );
}

    