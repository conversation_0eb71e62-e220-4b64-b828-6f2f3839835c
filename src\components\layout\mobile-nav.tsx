
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  ShoppingCart,
  Truck,
  Undo2,
  Users,
  Building,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetTitle,
} from "@/components/ui/sheet"
import { Button } from '../ui/button';
import { PanelLeft, BarChart3, Settings, Boxes, Wallet } from 'lucide-react';

const mainLinks = [
  { href: '/dashboard', label: 'الرئيسية', icon: LayoutDashboard },
  { href: '/sales', label: 'المبيعات', icon: ShoppingCart },
  { href: '/purchases', label: 'المشتريات', icon: Truck },
  { href: '/returns', label: 'المرتجعات', icon: Undo2 },
];

const moreLinks = [
    { href: "/customers", label: "العملاء", icon: Users },
    { href: "/suppliers", label: "الموردون", icon: Building },
    { href: "/inventory", label: "المخزون", icon: Boxes },
    { href: "/cashbox", label: "صندوق النقد", icon: Wallet },
    { href: "/reports", label: "التقارير", icon: BarChart3 },
    { href: "/settings", label: "الإعدادات", icon: Settings },
];

export default function MobileNav() {
  const pathname = usePathname();

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-40 border-t bg-background/95 backdrop-blur-sm lg:hidden">
      <div className="grid h-16 grid-cols-5 items-center justify-around">
        {mainLinks.map((link) => {
          const isActive = pathname.startsWith(link.href);
          return (
            <Link
              key={link.href}
              href={link.href}
              className="flex flex-col items-center justify-center gap-1 text-xs"
            >
              <link.icon
                className={cn(
                  'h-5 w-5',
                  isActive ? 'text-primary' : 'text-muted-foreground'
                )}
              />
              <span
                className={cn(
                  'truncate',
                  isActive ? 'font-bold text-primary' : 'text-muted-foreground'
                )}
              >
                {link.label}
              </span>
            </Link>
          );
        })}
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="flex flex-col items-center justify-center h-full w-full text-xs gap-1 text-muted-foreground">
                    <PanelLeft className="h-5 w-5" />
                    <span>المزيد</span>
                </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-auto">
                 <SheetTitle className="sr-only">قائمة إضافية</SheetTitle>
                 <div className="grid grid-cols-3 gap-4 p-4">
                     {moreLinks.map((link) => {
                        const isActive = pathname.startsWith(link.href);
                        return (
                            <Link
                                key={link.href}
                                href={link.href}
                                className="flex flex-col items-center justify-center gap-2 rounded-lg p-2 text-center hover:bg-accent"
                            >
                                <link.icon
                                    className={cn(
                                    'h-6 w-6',
                                    isActive ? 'text-primary' : 'text-muted-foreground'
                                    )}
                                />
                                <span
                                    className={cn(
                                    'text-sm',
                                    isActive ? 'font-bold text-primary' : 'text-muted-foreground'
                                    )}
                                >
                                    {link.label}
                                </span>
                            </Link>
                        );
                     })}
                 </div>
            </SheetContent>
        </Sheet>
      </div>
    </nav>
  );
}

    