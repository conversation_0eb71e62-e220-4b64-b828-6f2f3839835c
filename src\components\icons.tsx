
import type { SVGProps } from "react";

export function Logo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      width="1em"
      height="1em"
      {...props}
    >
      <text 
        x="50%" 
        y="50%" 
        textAnchor="middle" 
        dy=".3em" 
        fontSize="40" 
        fill="currentColor" 
        fontFamily="Arial, sans-serif" 
        fontWeight="bold"
      >
        ع
      </text>
    </svg>
  );
}
