
"use client";

import { useState, useMemo, useEffect } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { createRoot } from 'react-dom/client';
import * as XLSX from 'xlsx';
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { DatePickerWithRange } from "@/components/ui/date-picker";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel, SelectSeparator } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileDown, Search, Printer, TrendingUp, BarChart, FileText, Package, AlertTriangle, Users2, Wallet, History, ArrowUpCircle, ArrowDownCircle, Banknote, Receipt, Undo2, ChevronsUpDown, ChevronsDown, ChevronsUp, Warehouse } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Bar, BarChart as RechartsBarChart, CartesianGrid, XAxis, YAxis, Tooltip } from "recharts"
import { Badge } from "@/components/ui/badge";
import { ReportTemplate, type ReportTemplateProps } from "@/components/report/report-template";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { useStore } from "@/store/erp-store";
import { DateRange } from "react-day-picker";
import { differenceInDays, parseISO, format, subDays, startOfDay, endOfDay, isWithinInterval, parse } from 'date-fns';
import type { Product, SaleRecord } from "@/store/erp-store";


export default function ReportsPage() {
    const { sales, purchases, customers, suppliers, inventory, cashboxTransactions, returns, settings } = useStore();
    const searchParams = useSearchParams();
    const router = useRouter();
    
    const [reportData, setReportData] = useState<any>(null);
    const [activeReportType, setActiveReportType] = useState<string>(searchParams.get('reportType') || 'sales');
    const { toast } = useToast();

    const [dateRange, setDateRange] = useState<DateRange | undefined>({ from: subDays(new Date(), 30), to: new Date()});
    const [selectedCustomerId, setSelectedCustomerId] = useState<string>("all");
    const [selectedSupplierId, setSelectedSupplierId] = useState<string>("all");
    const [selectedProductName, setSelectedProductName] = useState<string>("");
    const [selectedAccountId, setSelectedAccountId] = useState<string>(searchParams.get('accountId') || "");
    const [invoiceStatusFilter, setInvoiceStatusFilter] = useState<'all' | 'paid' | 'unpaid' | 'partial'>('all');
    

    const generateReport = (reportType = activeReportType, accountId = selectedAccountId) => {
        let data: any = null;
        const from = dateRange?.from ? startOfDay(dateRange.from) : undefined;
        const to = dateRange?.to ? endOfDay(dateRange.to) : undefined;

        const isDateInRange = (dateStr: string) => {
            if (!from || !to) return true;
            try {
                // The date from the store is already in 'yyyy-MM-dd' format.
                const date = parse(dateStr, 'yyyy-MM-dd', new Date());
                return isWithinInterval(date, { start: from, end: to });
            } catch (e) {
                console.error("Invalid date format:", dateStr);
                return false;
            }
        };

        switch(reportType) {
            case 'sales':
                const filteredSales = sales.filter(s => 
                    isDateInRange(s.date) && 
                    (selectedCustomerId === 'all' || s.customerId.toString() === selectedCustomerId)
                );
                data = {
                    title: "تقرير المبيعات",
                    rows: filteredSales,
                    headers: ['رقم الفاتورة', 'التاريخ', 'العميل', 'الإجمالي'],
                };
                break;
            case 'purchases':
                 const filteredPurchases = purchases.filter(p => 
                    isDateInRange(p.date) && 
                    (selectedSupplierId === 'all' || p.supplierId.toString() === selectedSupplierId)
                 );
                data = {
                    title: "تقرير المشتريات",
                    rows: filteredPurchases,
                    headers: ['رقم الفاتورة', 'التاريخ', 'المورد', 'الإجمالي'],
                 };
                break;
            case 'account': {
                if (!accountId) {
                    toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء اختيار حساب لعرض الكشف.' });
                    return;
                }
                const [type, id] = accountId.split('-');
                const person = type === 'customer' ? customers.find(c => c.id.toString() === id) : suppliers.find(s => s.id.toString() === id);

                let allTransactions: any[] = [];
                if (type === 'customer') {
                    const customerSales = sales.filter(s => s.customerId.toString() === id);
                    const customerReturns = returns.filter(r => r.type === 'sales' && r.customerOrSupplierId.toString() === id);
                    const customerPayments = cashboxTransactions.filter(t => (t.type === 'income' && t.description.includes(`العميل: ${person?.name}`)) || (t.type === 'income' && t.description.includes(`فاتورة`)) && customerSales.some(s => t.description.includes(s.id)));


                    allTransactions = [
                        ...customerSales.map(s => ({ date: s.date, type: 'فاتورة مبيعات', description: `فاتورة #${s.id}`, invoiceId: s.id, debit: s.totalAmount, credit: 0, isInvoice: true, status: s.status, paid: s.paidAmount })),
                        ...customerPayments.map(p => ({ date: p.date, type: 'دفعة مستلمة', description: p.description, invoiceId: null, debit: 0, credit: p.amount, isInvoice: false, status: 'paid', paid: p.amount })),
                        ...customerReturns.map(r => ({ date: r.date, type: 'مرتجع مبيعات', description: `مرتجع من فاتورة #${r.originalInvoiceId}`, invoiceId: null, debit: 0, credit: r.amount, isInvoice: false, status: 'paid', paid: r.amount })),
                    ];
                } else { // Supplier
                     const supplierPurchases = purchases.filter(p => p.supplierId.toString() === id);
                     const supplierReturns = returns.filter(r => r.type === 'purchase' && r.customerOrSupplierId.toString() === id);
                     const supplierPayments = cashboxTransactions.filter(t => (t.type === 'expense' && t.description.includes(`المورد: ${person?.name}`)) || (t.type === 'expense' && t.description.includes(`فاتورة`)) && supplierPurchases.some(p => t.description.includes(p.id)));

                     allTransactions = [
                        ...supplierPurchases.map(p => ({ date: p.date, type: 'فاتورة مشتريات', description: `فاتورة #${p.id}`, invoiceId: p.id, debit: 0, credit: p.totalAmount, isInvoice: true, status: p.status, paid: p.paidAmount })),
                        ...supplierPayments.map(p => ({ date: p.date, type: 'دفعة مدفوعة', description: p.description, invoiceId: null, debit: p.amount, credit: 0, isInvoice: false, status: 'paid', paid: p.amount })),
                        ...supplierReturns.map(r => ({ date: r.date, type: 'مرتجع مشتريات', description: `مرتجع من فاتورة #${r.originalInvoiceId}`, invoiceId: null, debit: r.amount, credit: 0, isInvoice: false, status: 'paid', paid: r.amount })),
                    ];
                }
                
                const sortedTransactions = allTransactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
                
                 const calculateBalanceForTransactions = (transactions: any[], personType: 'customer' | 'supplier') => {
                    return transactions.reduce((acc, t) => {
                        const debit = t.debit || 0;
                        const credit = t.credit || 0;
                        return acc + debit - credit;
                    }, 0);
                }

                const openingBalance = from 
                    ? calculateBalanceForTransactions(sortedTransactions.filter(t => new Date(t.date) < startOfDay(from)), type as 'customer' | 'supplier')
                    : 0;

                const transactionsInDateRange = sortedTransactions.filter(t => isDateInRange(t.date));

                const transactionsFilteredByStatus = invoiceStatusFilter === 'all'
                    ? transactionsInDateRange
                    : transactionsInDateRange.filter(t => {
                        if (!t.isInvoice) return true; // Keep non-invoice transactions like payments/returns
                        if (invoiceStatusFilter === 'unpaid') return t.status === 'unpaid' || t.status === 'partial';
                        return t.status === invoiceStatusFilter;
                    });
                
                const addRunningBalance = (transactions: any[], openingBalance: number) => {
                    let balance = openingBalance;
                    return transactions.map(t => {
                         balance = balance + (t.debit || 0) - (t.credit || 0);
                        return { ...t, balance };
                    });
                }
                
                const statementRows = addRunningBalance(transactionsFilteredByStatus, openingBalance);

                const totalDebit = statementRows.reduce((acc, t) => acc + (t.debit || 0), 0);
                const totalCredit = statementRows.reduce((acc, t) => acc + (t.credit || 0), 0);
                const finalBalance = statementRows.length > 0 ? statementRows[statementRows.length - 1].balance : openingBalance;
                
                data = {
                    title: `كشف حساب: ${person?.name}`,
                    rows: statementRows,
                    headers: ['التاريخ', 'نوع العملية', 'البيان', 'مدين', 'دائن', 'الرصيد'],
                    summary: {
                        openingBalance,
                        totalDebit,
                        totalCredit,
                        finalBalance
                    }
                };
                break;
            }
            case 'item_movement': {
                if (!selectedProductName) {
                    toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء اختيار مادة لعرض التقرير.' });
                    return;
                }
                
                const productBatches = inventory.filter(p => p.name === selectedProductName);
                if (productBatches.length === 0) return;

                const allTx = [
                    ...sales.flatMap(s => s.items.filter(i => i.name === selectedProductName).map(i => ({ date: s.date, type: 'بيع', quantityIn: 0, quantityOut: (i.quantity + i.bonus), invoiceId: s.id, batchNumber: i.batchNumber, price: i.price }))),
                    ...purchases.flatMap(p => p.items.filter(i => i.name === selectedProductName).map(i => ({ date: p.date, type: 'شراء', quantityIn: (i.quantity + i.bonus), quantityOut: 0, invoiceId: p.id, batchNumber: i.batchNumber, price: i.price }))),
                    ...returns.flatMap(r => r.items.filter(i => i.name === selectedProductName).map(i => ({ date: r.date, type: r.type === 'sales' ? 'مرتجع بيع' : 'مرتجع شراء', quantityIn: r.type === 'sales' ? (i.returnQuantity + i.returnBonus) : 0, quantityOut: r.type === 'purchase' ? (i.returnQuantity + i.returnBonus) : 0, invoiceId: r.id, batchNumber: i.batchNumber, price: i.price })))
                ].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
                
                const calculateOpeningStock = () => {
                    if (!from) return 0;
                    const txBeforeDate = allTx.filter(tx => new Date(tx.date) < startOfDay(from));
                    return txBeforeDate.reduce((acc, tx) => acc + tx.quantityIn - tx.quantityOut, 0);
                };
                
                const openingStock = calculateOpeningStock();
                const transactionsInDateRange = allTx.filter(tx => isDateInRange(tx.date));
                
                let stockBalance = openingStock;
                const movementRows = transactionsInDateRange.map(tx => {
                    stockBalance += tx.quantityIn - tx.quantityOut;
                    return { ...tx, balance: stockBalance };
                });
                
                const totalIn = transactionsInDateRange.reduce((acc, tx) => acc + tx.quantityIn, 0);
                const totalOut = transactionsInDateRange.reduce((acc, tx) => acc + tx.quantityOut, 0);

                data = {
                    title: `تقرير حركة مادة: ${selectedProductName}`,
                    productBatches: productBatches,
                    rows: movementRows,
                    summary: {
                        openingStock,
                        totalIn,
                        totalOut,
                        finalBalance: openingStock + totalIn - totalOut,
                    }
                 };
                break;
            }
            case 'cashbox': {
                const filteredTxs = cashboxTransactions.filter(t => isDateInRange(t.date));
                const startBalance = from ? cashboxTransactions.filter(t => new Date(t.date) < startOfDay(from)).reduce((acc, t) => acc + (t.type === 'income' ? t.amount : -t.amount), 0) : 0;
                let runningBalance = startBalance;

                const rows = filteredTxs.map(tx => {
                    runningBalance += (tx.type === 'income' ? tx.amount : -tx.amount);
                    return { ...tx, balance: runningBalance };
                });

                const totalIncome = filteredTxs.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
                const totalExpense = filteredTxs.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
                const endBalance = startBalance + totalIncome - totalExpense;

                data = {
                    title: "تقرير الصندوق اليومي",
                    rows: rows,
                    headers: ['التاريخ', 'البيان', 'المقبوضات', 'المدفوعات', 'الرصيد'],
                    summary: { startBalance, totalIncome, totalExpense, endBalance }
                };
                break;
            }
            case 'profit_loss':
                const salesInRange = sales.filter(s => isDateInRange(s.date));
                const revenue = salesInRange.reduce((acc, s) => acc + s.totalAmount, 0);
                const cogs = salesInRange.flatMap(s => s.items).reduce((acc, i) => acc + (i.purchasePrice * i.quantity), 0);
                const grossProfit = revenue - cogs;
                const expensesList = cashboxTransactions.filter(t => t.type === 'expense' && isDateInRange(t.date));
                const totalExpenses = expensesList.reduce((acc, t) => acc + t.amount, 0);
                const netProfit = grossProfit - totalExpenses;
                data = {
                    title: "تقرير الأرباح والخسائر",
                    summary: {
                        totalRevenue: revenue,
                        cogs: cogs,
                        grossProfit: grossProfit,
                        totalExpenses: totalExpenses,
                        netProfit: netProfit,
                    },
                    expenses: expensesList,
                }
                break;
        }
        setReportData(data);
    };

    useEffect(() => {
        const reportType = searchParams.get('reportType');
        const accountId = searchParams.get('accountId');

        if (reportType === 'account' && accountId) {
            setActiveReportType(reportType);
            setSelectedAccountId(accountId);
            // We use a timeout to ensure the state update for the select input has rendered
            setTimeout(() => generateReport(reportType, accountId), 100);
        }
    }, [searchParams]);

    const handlePrint = () => {
        if (!reportData) return;

        const dateRangeString = dateRange?.from && dateRange?.to 
            ? `للفترة من ${format(dateRange.from, "yyyy/MM/dd")} إلى ${format(dateRange.to, "yyyy/MM/dd")}`
            : "لكل الأوقات";

        const reportProps: ReportTemplateProps = {
            title: reportData.title,
            dateRange: dateRangeString,
            reportType: activeReportType,
            data: reportData,
            companyInfo: settings.companyInfo,
            printLogoSvg: settings.printLogoSvg,
        };

        const printWindow = window.open('', '_blank', 'width=1000,height=800');
        if (!printWindow) return;

        const printDocument = printWindow.document;
        printDocument.write('<html><head><title>طباعة التقرير</title>');
        
        const styleSheets = Array.from(document.styleSheets);
        styleSheets.forEach(ss => {
            if (ss.href) {
                const link = printDocument.createElement('link');
                link.rel = 'stylesheet';
                link.href = ss.href;
                printDocument.head.appendChild(link);
            } else if (ss.ownerNode instanceof HTMLStyleElement) {
                 const style = printDocument.createElement('style');
                 style.textContent = ss.ownerNode.innerHTML;
                 printDocument.head.appendChild(style);
            }
        });

        const waitForStyles = new Promise<void>((resolve) => {
            let loadedCount = 0;
            const links = printDocument.head.getElementsByTagName('link');
            if (links.length === 0) {
                resolve();
                return;
            }
            const totalLinks = links.length;
            for (let i = 0; i < totalLinks; i++) {
                links[i].onload = () => {
                    loadedCount++;
                    if (loadedCount === totalLinks) {
                        resolve();
                    }
                };
                 links[i].onerror = () => {
                    loadedCount++;
                    if (loadedCount === totalLinks) {
                        resolve();
                    }
                };
            }
        });

        waitForStyles.then(() => {
            printDocument.write('<style>@media print { @page { size: A4; margin: 0; } body { -webkit-print-color-adjust: exact; print-color-adjust: exact; margin: 1.6cm; } } </style>');
            printDocument.write('</head><body dir="rtl"></body></html>');
            printDocument.close(); 
            
            const printContentEl = printDocument.createElement('div');
            printDocument.body.appendChild(printContentEl);
            
            const root = createRoot(printContentEl);
            root.render(<ReportTemplate {...reportProps} />);
            
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }, 500); 
        });
    };

    const handleExportExcel = () => {
        if (!reportData) {
            toast({
                variant: 'destructive',
                title: 'لا توجد بيانات',
                description: 'الرجاء توليد تقرير أولاً قبل محاولة التصدير.',
            });
            return;
        }

        let worksheetData: any[] = [];
        let fileName = `${reportData.title.replace(/[\s/:]+/g, '_')}.xlsx`;

        switch (activeReportType) {
            case 'sales':
            case 'purchases':
                worksheetData = reportData.rows.flatMap((invoice: any) => 
                    invoice.items.map((item: any) => ({
                        'رقم الفاتورة': invoice.id,
                        'التاريخ': invoice.date,
                        [activeReportType === 'sales' ? 'العميل' : 'المورد']: invoice.customerName || invoice.supplierName,
                        'إجمالي الفاتورة': invoice.totalAmount,
                        'المادة': item.name,
                        'الكمية': item.quantity,
                        'البونص': item.bonus,
                        'السعر': item.price,
                        'إجمالي المادة': item.total
                    }))
                );
                break;
            
            case 'account':
                if (reportData.summary) {
                    worksheetData.push({ 'البيان': 'رصيد بداية المدة', 'الرصيد': reportData.summary.openingBalance });
                }
                worksheetData = worksheetData.concat(reportData.rows.map((row: any) => ({
                    'التاريخ': row.date,
                    'نوع العملية': row.type,
                    'البيان': row.description,
                    'مدين': row.debit,
                    'دائن': row.credit,
                    'الرصيد': row.balance
                })));
                if (reportData.summary) {
                    worksheetData.push({}); // empty row
                    worksheetData.push({
                        'التاريخ': 'الإجماليات',
                        'مدين': reportData.summary.totalDebit,
                        'دائن': reportData.summary.totalCredit,
                        'الرصيد': reportData.summary.finalBalance,
                    });
                }
                break;

            case 'item_movement':
                 worksheetData = reportData.rows.map((row: any) => ({
                    'التاريخ': row.date,
                    'النوع': row.type,
                    'رقم الفاتورة': row.invoiceId,
                    'رقم الوجبة': row.batchNumber,
                    'داخل': row.quantityIn,
                    'خارج': row.quantityOut,
                    'الرصيد': row.balance,
                }));
                 break;

            case 'cashbox':
                 worksheetData = reportData.rows;
                 break;

            case 'profit_loss':
                if (reportData.summary) {
                    const { summary, expenses } = reportData;
                    worksheetData = [
                        { 'البيان': 'إجمالي إيرادات المبيعات', 'المبلغ': summary.totalRevenue },
                        { 'البيان': 'تكلفة البضاعة المباعة (COGS)', 'المبلغ': summary.cogs },
                        { 'البيان': 'هامش الربح الإجمالي', 'المبلغ': summary.grossProfit },
                        { 'البيان': '--- مصاريف ---', 'المبلغ': ''},
                        ...expenses.map((exp:any) => ({ 'البيان': exp.description, 'المبلغ': exp.amount })),
                        { 'البيان': 'إجمالي المصاريف', 'المبلغ': summary.totalExpenses },
                        { 'البيان': 'صافي الربح / الخسارة', 'المبلغ': summary.netProfit },
                    ];
                }
                break;

            default:
                 toast({
                    variant: 'destructive',
                    title: 'تصدير غير مدعوم',
                    description: `التصدير غير مدعوم لهذا النوع من التقارير (${activeReportType}).`,
                });
                return;
        }
        
        if (worksheetData.length === 0) {
            toast({
                variant: 'destructive',
                title: 'لا توجد بيانات للتصدير',
                description: 'البيانات الموجودة في التقرير فارغة.',
            });
            return;
        }
        
        const worksheet = XLSX.utils.json_to_sheet(worksheetData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'التقرير');
        XLSX.writeFile(workbook, fileName);
    };

    const uniqueProductNames = useMemo(() => {
        const names = new Set<string>();
        inventory.forEach(item => names.add(item.name));
        return Array.from(names);
    }, [inventory]);
    
    const dateRangeString = dateRange?.from && dateRange?.to 
            ? `للفترة من ${format(dateRange.from, "yyyy/MM/dd")} إلى ${format(dateRange.to, "yyyy/MM/dd")}`
            : "لكل الأوقات";

  return (
    <>
      <PageHeader
        title="التقارير والتحليلات"
        description="إنشاء تقارير متنوعة للمبيعات والمشتريات والأرباح."
        className="no-print"
         action={
            <div className="flex gap-2">
                <Button variant="outline" onClick={handleExportExcel} disabled={!reportData}>
                    <FileDown className="ml-2 h-4 w-4" />
                    تصدير Excel
                </Button>
                <Button onClick={handlePrint} disabled={!reportData}>
                    <Printer className="ml-2 h-4 w-4" />
                    طباعة التقرير الحالي
                </Button>
            </div>
        }
      />
      
      <Tabs value={activeReportType} onValueChange={setActiveReportType} className="w-full no-print">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 lg:grid-cols-7">
            <TabsTrigger value="sales">المبيعات</TabsTrigger>
            <TabsTrigger value="purchases">المشتريات</TabsTrigger>
            <TabsTrigger value="account">كشف حساب</TabsTrigger>
            <TabsTrigger value="item_movement">حركة مادة</TabsTrigger>
            <TabsTrigger value="cashbox">الصندوق</TabsTrigger>
            <TabsTrigger value="profit_loss">الأرباح</TabsTrigger>
            <TabsTrigger value="analytics">تحليلات</TabsTrigger>
        </TabsList>

        <ReportFilterCard type="sales">
            <div className="grid md:grid-cols-3 gap-4">
                 <div className="space-y-2">
                    <Label>العميل (اختياري)</Label>
                    <Select value={selectedCustomerId} onValueChange={setSelectedCustomerId}>
                        <SelectTrigger><SelectValue/></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">كل العملاء</SelectItem>
                            {customers.map(c => <SelectItem key={c.id} value={c.id.toString()}>{c.name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange}/>
                </div>
                <Button className="self-end" onClick={() => generateReport()}>
                    <Search className="ml-2 h-4 w-4" />
                    توليد التقرير
                </Button>
            </div>
        </ReportFilterCard>

        <ReportFilterCard type="purchases">
            <div className="grid md:grid-cols-3 gap-4">
                <div className="space-y-2">
                    <Label>المورد (اختياري)</Label>
                    <Select value={selectedSupplierId} onValueChange={setSelectedSupplierId}>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                        <SelectContent>
                             <SelectItem value="all">كل الموردين</SelectItem>
                            {suppliers.map(s => <SelectItem key={s.id} value={s.id.toString()}>{s.name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange} />
                </div>
                <Button className="self-end" onClick={() => generateReport()}>
                        <Search className="ml-2 h-4 w-4" />
                    توليد التقرير
                </Button>
            </div>
        </ReportFilterCard>

        <ReportFilterCard type="account">
             <div className="grid md:grid-cols-4 gap-4">
                <div className="space-y-2">
                    <Label>اختر الحساب</Label>
                    <Select value={selectedAccountId} onValueChange={setSelectedAccountId}>
                        <SelectTrigger><SelectValue placeholder="اختر عميل أو مورد..." /></SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>العملاء</SelectLabel>
                                {customers.map(c => <SelectItem key={`customer-${c.id}`} value={`customer-${c.id}`}>{c.name}</SelectItem>)}
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                                <SelectLabel>الموردون</SelectLabel>
                                {suppliers.map(s => <SelectItem key={`supplier-${s.id}`} value={`supplier-${s.id}`}>{s.name}</SelectItem>)}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                 <div className="space-y-2">
                    <Label>حالة الفواتير</Label>
                    <Select value={invoiceStatusFilter} onValueChange={(value) => setInvoiceStatusFilter(value as any)}>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">الكل</SelectItem>
                            <SelectItem value="paid">مسددة</SelectItem>
                            <SelectItem value="unpaid">غير مسددة وجزئية</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange} />
                </div>
                <div className="self-end">
                    <Button className="w-full" onClick={() => generateReport()}>
                        <FileText className="ml-2 h-4 w-4" />
                        توليد الكشف
                    </Button>
                </div>
            </div>
        </ReportFilterCard>

        <ReportFilterCard type="item_movement">
             <div className="grid md:grid-cols-3 gap-4">
                <div className="space-y-2">
                    <Label>المادة</Label>
                    <Select value={selectedProductName} onValueChange={setSelectedProductName}>
                        <SelectTrigger><SelectValue placeholder="اختر مادة..." /></SelectTrigger>
                        <SelectContent>
                            {uniqueProductNames.map(name => <SelectItem key={name} value={name}>{name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </div>
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange} />
                </div>
                <Button className="self-end" onClick={() => generateReport()}>
                    <History className="ml-2 h-4 w-4" />
                    توليد التقرير
                </Button>
            </div>
        </ReportFilterCard>

         <ReportFilterCard type="cashbox">
            <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange} />
                </div>
                <Button className="self-end" onClick={() => generateReport()}>
                    <Wallet className="ml-2 h-4 w-4" />
                    توليد التقرير
                </Button>
            </div>
        </ReportFilterCard>

        <ReportFilterCard type="profit_loss">
            <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label>النطاق الزمني</Label>
                    <DatePickerWithRange onSelect={setDateRange} selected={dateRange} />
                </div>
                <Button className="self-end" onClick={() => generateReport()}>
                    <TrendingUp className="ml-2 h-4 w-4" />
                    توليد التقرير
                </Button>
            </div>
        </ReportFilterCard>

        <TabsContent value="analytics">
            <AnalyticsReports />
        </TabsContent>
      </Tabs>
      
        <div className="print-area mt-8">
            {reportData && activeReportType !== 'analytics' && (
                <Card>
                    <CardHeader>
                        <CardTitle className="text-2xl">{reportData.title}</CardTitle>
                        <CardDescription>{dateRangeString}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {activeReportType === 'sales' && <SalesResults data={reportData.rows} headers={reportData.headers} title={reportData.title} />}
                        {activeReportType === 'purchases' && <DetailedReportResults data={reportData.rows} headers={reportData.headers} title={reportData.title} />}
                        {activeReportType === 'account' && <AccountStatementResults data={reportData} />}
                        {activeReportType === 'item_movement' && <ItemMovementResults data={reportData} />}
                        {activeReportType === 'cashbox' && <CashboxReportResults data={reportData} />}
                        {activeReportType === 'profit_loss' && <ProfitLossReportResults data={reportData} />}
                    </CardContent>
                </Card>
            )}
        </div>
    </>
  );
}

const ReportFilterCard = ({ type, children }: { type: string, children: React.ReactNode }) => (
    <TabsContent value={type}>
        <Card className="mt-4">
            <CardHeader>
                <CardTitle>خيارات التقرير</CardTitle>
                <CardDescription>اختر الخيارات لتوليد التقرير المطلوب.</CardDescription>
            </CardHeader>
            <CardContent>
                {children}
            </CardContent>
        </Card>
    </TabsContent>
);


// Main component for Sales Report results
const SalesResults = ({ data, headers, title }: { data: any[], headers: string[], title: string }) => {
    const totalSales = data.reduce((sum, item) => sum + item.totalAmount, 0);

    const chartConfig = {
      total: { label: "المبيعات", color: "hsl(var(--primary))" },
    };
    
    const chartData = useMemo(() => data.map(item => ({ name: item.id, total: item.totalAmount })), [data]);

    return (
        <div className="grid gap-8 lg:grid-cols-5">
            <div className="lg:col-span-3">
                <h3 className="font-semibold text-lg mb-4">تفاصيل الفواتير</h3>
                <DetailedReportResults data={data} headers={headers} title={title} />
            </div>
            <div className="lg:col-span-2">
                <h3 className="font-semibold text-lg mb-4">ملخص المبيعات</h3>
                 <Card>
                    <CardHeader>
                        <CardTitle>مجموع المبيعات</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-3xl font-bold text-primary">{totalSales.toLocaleString('ar-IQ')} د.ع</p>
                    </CardContent>
                </Card>
                <ChartContainer config={chartConfig} className="h-[250px] w-full mt-6">
                    <RechartsBarChart
                        accessibilityLayer
                        data={chartData}
                        layout="vertical"
                        margin={{ right: 20 }}
                    >
                        <CartesianGrid horizontal={false} />
                         <YAxis
                            dataKey="name"
                            type="category"
                            tickLine={false}
                            tickMargin={10}
                            axisLine={false}
                            tickFormatter={(value) => value.substring(0,10)}
                        />
                        <XAxis dataKey="total" type="number" hide />
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent indicator="dot" />}
                        />
                        <Bar dataKey="total" layout="vertical" fill="var(--color-total)" radius={4} />
                    </RechartsBarChart>
                </ChartContainer>
            </div>
        </div>
    );
};


// Component for detailed reports (Sales, Purchases)
const DetailedReportResults = ({ data, headers, title }: { data: any[], headers: string[], title: string }) => {
    return (
        <Card className="print-card">
            <CardHeader className="hidden print-block">
                 <CardTitle className="print-title">{title}</CardTitle>
                 <CardDescription className="print-description">للفترة من ... إلى ...</CardDescription>
            </CardHeader>
            <CardContent>
                 <Accordion type="single" collapsible className="w-full">
                    {data.map((invoice, index) => (
                         <AccordionItem value={`item-${index}`} key={invoice.id}>
                             <AccordionTrigger className="hover:no-underline">
                                <div className="grid grid-cols-4 w-full text-sm text-right">
                                     <span>{invoice.id}</span>
                                     <span>{invoice.date}</span>
                                     <span>{invoice.customerName || invoice.supplierName}</span>
                                     <span className="font-semibold text-left">{invoice.totalAmount.toLocaleString('ar-IQ')} د.ع</span>
                                </div>
                             </AccordionTrigger>
                             <AccordionContent>
                                 <div className="px-4 py-2 bg-muted/50 rounded-md">
                                    <h4 className="font-semibold mb-2 text-xs">تفاصيل المواد:</h4>
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>المادة</TableHead>
                                                <TableHead>الكمية</TableHead>
                                                <TableHead>البونص</TableHead>
                                                <TableHead>السعر</TableHead>
                                                <TableHead className="text-left">الإجمالي</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {invoice.items.map((item: any, itemIndex: number) => (
                                                <TableRow key={itemIndex}>
                                                    <TableCell>{item.name}</TableCell>
                                                    <TableCell>{item.quantity}</TableCell>
                                                    <TableCell>{item.bonus || 0}</TableCell>
                                                    <TableCell>{item.price.toLocaleString('ar-IQ')} د.ع</TableCell>
                                                    <TableCell className="text-left">{item.total.toLocaleString('ar-IQ')} د.ع</TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                 </div>
                             </AccordionContent>
                         </AccordionItem>
                    ))}
                </Accordion>
            </CardContent>
        </Card>
    );
};


const ItemMovementResults = ({ data }: { data: any }) => {
    const { rows, summary, productBatches } = data;
    
    return (
        <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">رصيد الافتتاح</CardTitle><Warehouse className="h-4 w-4 text-muted-foreground" /></CardHeader>
                    <CardContent><div className="text-2xl font-bold">{summary.openingStock.toLocaleString()}</div></CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">إجمالي الداخل</CardTitle><ChevronsDown className="h-4 w-4 text-green-500" /></CardHeader>
                    <CardContent><div className="text-2xl font-bold text-green-600">{summary.totalIn.toLocaleString()}</div></CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">إجمالي الخارج</CardTitle><ChevronsUp className="h-4 w-4 text-red-500" /></CardHeader>
                    <CardContent><div className="text-2xl font-bold text-red-600">{summary.totalOut.toLocaleString()}</div></CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">الرصيد النهائي</CardTitle><ChevronsUpDown className="h-4 w-4 text-muted-foreground" /></CardHeader>
                    <CardContent><div className="text-2xl font-bold">{summary.finalBalance.toLocaleString()}</div></CardContent>
                </Card>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                     <CardHeader><CardTitle>أرصدة الوجبات الحالية</CardTitle></CardHeader>
                     <CardContent>
                        <Table>
                            <TableHeader><TableRow><TableHead>رقم الوجبة</TableHead><TableHead>الكمية الحالية</TableHead><TableHead className="text-right">سعر البيع</TableHead></TableRow></TableHeader>
                            <TableBody>
                                {productBatches.map((batch: Product) => (
                                    <TableRow key={batch.batchNumber}>
                                        <TableCell>{batch.batchNumber}</TableCell>
                                        <TableCell>{batch.stock.toLocaleString()}</TableCell>
                                        <TableCell className="text-right">{batch.sellingPrice.toLocaleString()} د.ع</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                     </CardContent>
                </Card>
                <Card>
                    <CardHeader><CardTitle>سجل الحركات التفصيلي</CardTitle></CardHeader>
                    <CardContent className="max-h-96 overflow-y-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>التاريخ</TableHead>
                                    <TableHead>النوع</TableHead>
                                    <TableHead>داخل</TableHead>
                                    <TableHead>خارج</TableHead>
                                    <TableHead className="text-right">الرصيد</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {rows.map((row: any, index: number) => (
                                    <TableRow key={index}>
                                        <TableCell>{row.date}</TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span>{row.type} <span className="text-muted-foreground text-xs">(#{row.invoiceId})</span></span>
                                                <span className="text-xs text-blue-600">الوجبة: {row.batchNumber}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-green-600 font-mono">{row.quantityIn > 0 ? `+${row.quantityIn.toLocaleString()}` : '-'}</TableCell>
                                        <TableCell className="text-red-600 font-mono">{row.quantityOut > 0 ? `-${row.quantityOut.toLocaleString()}`: '-'}</TableCell>
                                        <TableCell className="text-right font-mono">{row.balance.toLocaleString()}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};



const AccountStatementResults = ({ data }: { data: any }) => {
    const { rows, headers, summary } = data;
    
    const getIconForType = (type: string) => {
        if (type?.includes('فاتورة')) return <Receipt className="h-4 w-4 text-blue-500" />;
        if (type?.includes('دفعة')) return <Banknote className="h-4 w-4 text-green-500" />;
        if (type?.includes('مرتجع')) return <Undo2 className="h-4 w-4 text-orange-500" />;
        return <FileText className="h-4 w-4 text-gray-500" />;
    };
    
    if (!summary) return null;

    return (
        <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">رصيد بداية المدة</CardTitle>
                        <Wallet className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{(summary.openingBalance || 0).toLocaleString('ar-IQ')} د.ع</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي المدين</CardTitle>
                        <ArrowDownCircle className="h-4 w-4 text-red-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">{(summary.totalDebit || 0).toLocaleString('ar-IQ')} د.ع</div>
                    </CardContent>
                </Card>
                 <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي الدائن</CardTitle>
                        <ArrowUpCircle className="h-4 w-4 text-green-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{(summary.totalCredit || 0).toLocaleString('ar-IQ')} د.ع</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">الرصيد النهائي</CardTitle>
                        <Banknote className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{(summary.finalBalance || 0).toLocaleString('ar-IQ')} د.ع</div>
                    </CardContent>
                </Card>
            </div>

            {/* Transactions Table */}
            <Card className="print-card">
                <CardContent className="pt-6">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[120px]">التاريخ</TableHead>
                                    <TableHead>نوع العملية</TableHead>
                                    <TableHead>البيان</TableHead>
                                    <TableHead className="text-center">مدين</TableHead>
                                    <TableHead className="text-center">دائن</TableHead>
                                    <TableHead className="text-right">الرصيد</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {rows.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={6} className="text-center h-24">
                                            لا توجد حركات خلال الفترة المحددة.
                                        </TableCell>
                                    </TableRow>
                                )}
                                {rows.map((row: any, rowIndex: number) => (
                                    <TableRow key={rowIndex}>
                                        <TableCell>{row.date}</TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                {getIconForType(row.type)}
                                                <span>{row.type || 'N/A'}</span>
                                                 {row.status && row.isInvoice && (
                                                    <Badge variant={row.status === 'paid' ? 'secondary' : row.status === 'partial' ? 'default' : 'destructive'} className={cn(row.status === 'paid' && "bg-green-100 text-green-800", row.status === 'partial' && "bg-yellow-100 text-yellow-800")}>
                                                        {row.status === 'paid' ? 'مسددة' : row.status === 'partial' ? 'جزئية' : 'غير مسددة'}
                                                    </Badge>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-muted-foreground">{row.description}</TableCell>
                                        <TableCell className="font-mono text-red-600 text-center">{row.debit > 0 ? row.debit.toLocaleString('ar-IQ') : '-'}</TableCell>
                                        <TableCell className="font-mono text-green-600 text-center">{row.credit > 0 ? row.credit.toLocaleString('ar-IQ') : '-'}</TableCell>
                                        <TableCell className="font-mono text-right">{row.balance.toLocaleString('ar-IQ')}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};


const CashboxReportResults = ({ data }: { data: any }) => {
    const { rows, headers, summary } = data;
    if (!summary) {
        return <p className="text-muted-foreground text-center p-4">لا توجد بيانات كافية لعرض ملخص الصندوق لهذه الفترة.</p>
    }
    return (
        <Card className="print-card">
            <CardHeader className="hidden print-block">
                 <CardTitle className="print-title">{data.title}</CardTitle>
                 <CardDescription className="print-description">للفترة من ... إلى ...</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 text-center">
                    <div className="p-4 bg-muted rounded-lg">
                        <p className="text-sm text-muted-foreground">رصيد بداية المدة</p>
                        <p className="text-lg font-bold">{(summary.startBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                    </div>
                     <div className="p-4 bg-green-100 dark:bg-green-900 rounded-lg">
                        <p className="text-sm text-green-700 dark:text-green-300">إجمالي المقبوضات</p>
                        <p className="text-lg font-bold text-green-800 dark:text-green-200">{(summary.totalIncome || 0).toLocaleString('ar-IQ')} د.ع</p>
                    </div>
                     <div className="p-4 bg-red-100 dark:bg-red-900 rounded-lg">
                        <p className="text-sm text-red-700 dark:text-red-300">إجمالي المدفوعات</p>
                        <p className="text-lg font-bold text-red-800 dark:text-red-200">{(summary.totalExpense || 0).toLocaleString('ar-IQ')} د.ع</p>
                    </div>
                     <div className="p-4 bg-muted rounded-lg">
                        <p className="text-sm text-muted-foreground">رصيد نهاية المدة</p>
                        <p className="text-lg font-bold">{(summary.endBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headers.map(h => <TableHead key={h}>{h}</TableHead>)}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                           {rows.map((row: any, index: number) => (
                               <TableRow key={index}>
                                   <TableCell>{row.date}</TableCell>
                                   <TableCell>{row.description}</TableCell>
                                   <TableCell className="text-green-600 font-mono">{row.type === 'income' ? row.amount.toLocaleString('ar-IQ') : '0'}</TableCell>
                                   <TableCell className="text-red-600 font-mono">{row.type === 'expense' ? row.amount.toLocaleString('ar-IQ') : '0'}</TableCell>
                                   <TableCell className="font-mono">{row.balance.toLocaleString('ar-IQ')}</TableCell>
                               </TableRow>
                           ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    );
};


// Component for Profit & Loss report
const ProfitLossReportResults = ({ data }: { data: any }) => {
    const { summary, expenses } = data;
    
    if (!summary) {
        return <p className="text-muted-foreground text-center p-4">لا توجد بيانات كافية لعرض تقرير الأرباح والخسائر لهذه الفترة.</p>
    }

    return (
        <Card className="print-card">
            <CardHeader className="hidden print-block">
                <CardTitle className="print-title">تقرير الأرباح والخسائر</CardTitle>
                <CardDescription className="print-description">للفترة من ... إلى ...</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6 md:grid-cols-2">
                {/* Summary Section */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium text-primary border-b pb-2">الملخص المالي</h3>
                    <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                            <span>إجمالي إيرادات المبيعات</span>
                            <span className="font-semibold">{(summary.totalRevenue || 0).toLocaleString('ar-IQ')} د.ع</span>
                        </div>
                        <div className="flex justify-between">
                            <span>(-) تكلفة البضاعة المباعة (COGS)</span>
                            <span className="font-semibold">({(summary.cogs || 0).toLocaleString('ar-IQ')}) د.ع</span>
                        </div>
                        <div className="flex justify-between font-bold text-base border-t pt-2">
                            <span>= هامش الربح الإجمالي</span>
                            <span className="text-green-600">{(summary.grossProfit || 0).toLocaleString('ar-IQ')} د.ع</span>
                        </div>
                        <div className="flex justify-between pt-4">
                            <span>(-) إجمالي المصاريف</span>
                            <span className="font-semibold">({(summary.totalExpenses || 0).toLocaleString('ar-IQ')}) د.ع</span>
                        </div>
                         <div className="flex justify-between font-bold text-lg border-t-2 border-primary pt-2 mt-2">
                            <span>= صافي الربح / الخسارة</span>
                            <span className={cn(summary.netProfit >= 0 ? 'text-green-700' : 'text-red-700')}>
                                {(summary.netProfit || 0).toLocaleString('ar-IQ')} د.ع
                            </span>
                        </div>
                    </div>
                </div>

                {/* Expenses Breakdown */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium text-primary border-b pb-2">تفصيل المصاريف</h3>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>البيان</TableHead>
                                    <TableHead className="text-left">المبلغ</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {expenses.map((expense: any, index: number) => (
                                    <TableRow key={index}>
                                        <TableCell>{expense.description}</TableCell>
                                        <TableCell className="text-left">{expense.amount.toLocaleString('ar-IQ')} د.ع</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                             <tfoot className="border-t">
                                <TableRow>
                                    <TableHead>الإجمالي</TableHead>
                                    <TableHead className="text-left font-bold">{(summary.totalExpenses || 0).toLocaleString('ar-IQ')} د.ع</TableHead>
                                </TableRow>
                            </tfoot>
                        </Table>
                    </div>
                </div>
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground mt-4">
                * هذا التقرير يعتمد على البيانات المسجلة في النظام خلال الفترة المحددة.
            </CardFooter>
        </Card>
    );
};


// Component for Analytics reports
const AnalyticsReports = () => {
    const { inventory, sales, customers } = useStore();

    const topSellingDrugs = useMemo(() => {
        const productSales = sales
            .flatMap(s => s.items)
            .reduce((acc, item) => {
                const key = `${item.name} (${item.batchNumber})`;
                if (!acc[key]) {
                    acc[key] = { name: item.name, sales: 0, revenue: 0 };
                }
                acc[key].sales += item.quantity;
                acc[key].revenue += item.total;
                return acc;
            }, {} as Record<string, { name: string; sales: number; revenue: number }>);

        return Object.values(productSales)
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 10);
    }, [sales]);

    const topCustomers = useMemo(() => {
         const customerSales = sales
            .reduce((acc, sale) => {
                if(!acc[sale.customerName]) {
                    acc[sale.customerName] = { invoices: 0, totalSales: 0 };
                }
                acc[sale.customerName].invoices++;
                acc[sale.customerName].totalSales += sale.totalAmount;
                return acc;
            }, {} as Record<string, { invoices: number, totalSales: number}>);

        return Object.entries(customerSales)
            .sort(([,a], [,b]) => b.totalSales - a.totalSales)
            .slice(0, 5)
            .map(([name, data]) => ({ name, ...data}));
    }, [sales]);

    const stockAlerts = useMemo(() => {
        const expiringSoon = inventory.filter(item => {
            const daysToExpiry = differenceInDays(parseISO(item.expDate), new Date());
            return daysToExpiry >= 0 && daysToExpiry <= 60;
        });

        const lowStock = inventory.filter(item => item.stock > 0 && item.stock < 20);

        const expired = inventory.filter(item => differenceInDays(parseISO(item.expDate), new Date()) < 0);

        return { expiringSoon, lowStock, expired };
    }, [inventory]);

    const topSellingChartConfig = {
      sales: { label: "المبيعات", color: "hsl(var(--primary))" },
    };
    const topSellingChartData = topSellingDrugs.map(drug => ({ name: drug.name, sales: drug.revenue }));
    
    const topCustomersChartConfig = {
      sales: { label: "إجمالي المبيعات", color: "hsl(var(--primary))" },
    };
    const topCustomersChartData = topCustomers.map(customer => ({ name: customer.name, sales: customer.totalSales }));

    return (
        <div className="grid gap-8 mt-4">
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <Package className="h-5 w-5 text-primary"/>
                        <CardTitle>تقرير الأدوية الأكثر مبيعًا</CardTitle>
                    </div>
                    <CardDescription>عرض الأدوية الأكثر رواجًا حسب الإيرادات.</CardDescription>
                </CardHeader>
                <CardContent className="grid gap-6 md:grid-cols-2">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>اسم الدواء</TableHead>
                                    <TableHead>الوحدات المباعة</TableHead>
                                    <TableHead className="text-left">الإيرادات</TableHead>
                                </TableRow>
                            </TableHeader>
                             <TableBody>
                                {topSellingDrugs.map((drug) => (
                                    <TableRow key={drug.name}>
                                        <TableCell><div className="font-medium">{drug.name}</div></TableCell>
                                        <TableCell>{drug.sales.toLocaleString('ar-IQ')}</TableCell>
                                        <TableCell className="text-left">{drug.revenue.toLocaleString('ar-IQ')} د.ع</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                     <div>
                        <ChartContainer config={topSellingChartConfig} className="h-[300px] w-full">
                            <RechartsBarChart
                                accessibilityLayer
                                data={topSellingChartData}
                                layout="vertical"
                                margin={{ left: 10, right: 10 }}
                            >
                                <CartesianGrid horizontal={false} />
                                <YAxis
                                    dataKey="name"
                                    type="category"
                                    tickLine={false}
                                    tickMargin={10}
                                    axisLine={false}
                                    width={110}
                                    tickFormatter={(value) => value.length > 15 ? `${value.substring(0, 15)}...` : value}
                                />
                                <XAxis dataKey="sales" type="number" />
                                <Tooltip
                                    cursor={false}
                                    content={<ChartTooltipContent indicator="dot" />}
                                />
                                <Bar dataKey="sales" fill="var(--color-sales)" radius={4} />
                            </RechartsBarChart>
                        </ChartContainer>
                    </div>
                </CardContent>
            </Card>

             <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <Users2 className="h-5 w-5 text-primary"/>
                        <CardTitle>تقرير العملاء الأكثر شراءً</CardTitle>
                    </div>
                    <CardDescription>عرض العملاء الأكثر مساهمة في المبيعات.</CardDescription>
                </CardHeader>
                <CardContent className="grid gap-6 md:grid-cols-2">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>اسم العميل</TableHead>
                                    <TableHead>عدد الفواتير</TableHead>
                                    <TableHead className="text-left">إجمالي المبيعات</TableHead>
                                </TableRow>
                            </TableHeader>
                             <TableBody>
                                {topCustomers.map((customer) => (
                                    <TableRow key={customer.name}>
                                        <TableCell><div className="font-medium">{customer.name}</div></TableCell>
                                        <TableCell>{customer.invoices.toLocaleString('ar-IQ')}</TableCell>
                                        <TableCell className="text-left">{customer.totalSales.toLocaleString('ar-IQ')} د.ع</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                     <div>
                        <ChartContainer config={topCustomersChartConfig} className="h-[300px] w-full">
                            <RechartsBarChart
                                accessibilityLayer
                                data={topCustomersChartData}
                                layout="vertical"
                                margin={{ left: 10, right: 10 }}
                            >
                                <CartesianGrid horizontal={false} />
                                <YAxis
                                    dataKey="name"
                                    type="category"
                                    tickLine={false}
                                    tickMargin={10}
                                    axisLine={false}
                                    width={110}
                                    tickFormatter={(value) => value.length > 15 ? `${value.substring(0, 15)}...` : value}
                                />
                                <XAxis dataKey="sales" type="number" />
                                <Tooltip
                                    cursor={false}
                                    content={<ChartTooltipContent indicator="dot" />}
                                />
                                <Bar dataKey="sales" fill="var(--color-sales)" radius={4} />
                            </RechartsBarChart>
                        </ChartContainer>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-destructive"/>
                        <CardTitle>تقرير تنبيهات المخزون</CardTitle>
                    </div>
                    <CardDescription>أدوية منتهية الصلاحية، أو تقترب من الانتهاء، أو كميتها منخفضة.</CardDescription>
                </CardHeader>
                <CardContent>
                     <Accordion type="multiple" defaultValue={['expiring', 'low_stock', 'expired']}>
                        <AccordionItem value="expiring">
                            <AccordionTrigger>أدوية تنتهي خلال 60 يوم ({stockAlerts.expiringSoon.length})</AccordionTrigger>
                            <AccordionContent>
                                <InventoryAlertTable items={stockAlerts.expiringSoon} />
                            </AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="low_stock">
                             <AccordionTrigger>أدوية ذات كمية منخفضة ({stockAlerts.lowStock.length})</AccordionTrigger>
                            <AccordionContent>
                                <InventoryAlertTable items={stockAlerts.lowStock} />
                            </AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="expired">
                             <AccordionTrigger>أدوية منتهية الصلاحية ({stockAlerts.expired.length})</AccordionTrigger>
                            <AccordionContent>
                                <InventoryAlertTable items={stockAlerts.expired} />
                            </AccordionContent>
                        </AccordionItem>
                     </Accordion>
                </CardContent>
            </Card>
        </div>
    )
}

const InventoryAlertTable = ({ items }: { items: Product[] }) => (
    <div className="overflow-x-auto">
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>اسم الدواء</TableHead>
                    <TableHead>الوجبة</TableHead>
                    <TableHead>الكمية المتبقية</TableHead>
                    <TableHead className="text-left">تاريخ الانتهاء</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {items.length === 0 && <TableRow><TableCell colSpan={4} className="text-center h-24">لا توجد عناصر لعرضها.</TableCell></TableRow>}
                {items.map(item => (
                    <TableRow key={`${item.id}-${item.batchNumber}`}>
                        <TableCell>{item.name}</TableCell>
                        <TableCell>{item.batchNumber}</TableCell>
                        <TableCell>{item.stock}</TableCell>
                        <TableCell className="text-left">{item.expDate}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    </div>
);

    


    




    

