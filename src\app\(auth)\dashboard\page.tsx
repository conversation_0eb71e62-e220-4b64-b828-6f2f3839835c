"use client";

import { useState, useEffect, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Sparkles,
  Zap,
  Target,
  Activity,
  Users,
  Package,
  DollarSign,
  ShoppingCart
} from "lucide-react";
import { useStore } from "@/store/erp-store";
import Link from "next/link";
import { 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from "recharts";
import { 
  ModernDashboardIcon,
  ModernInventoryIcon,
  ModernSalesIcon,
  ModernCustomersIcon,
  ModernCashboxIcon
} from "@/components/icons/modern-icons";
import { differenceInDays, parseISO, format, subDays } from 'date-fns';

const COLORS = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'];

export default function ModernDashboardPage() {
  const { sales, inventory, customers } = useStore();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const dashboardData = useMemo(() => {
    if (!isClient) {
      return {
        totalRevenue: 0,
        revenueChange: 0,
        totalSales: 0,
        salesChange: 0,
        totalCustomers: 0,
        customersChange: 0,
        lowStockItems: 0,
        chartData: [],
        topProducts: [],
        recentSales: []
      };
    }

    const today = new Date();
    const last30Days = subDays(today, 30);
    const last60Days = subDays(today, 60);

    // Revenue calculations
    const salesLast30Days = sales.filter(s => new Date(s.date) >= last30Days);
    const salesBeforeLast30Days = sales.filter(s => new Date(s.date) >= last60Days && new Date(s.date) < last30Days);
    
    const totalRevenue = salesLast30Days.reduce((acc, s) => acc + s.totalAmount, 0);
    const prevTotalRevenue = salesBeforeLast30Days.reduce((acc, s) => acc + s.totalAmount, 0);
    const revenueChange = prevTotalRevenue > 0 ? ((totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100 : 0;

    // Sales calculations
    const totalSales = salesLast30Days.length;
    const prevTotalSales = salesBeforeLast30Days.length;
    const salesChange = prevTotalSales > 0 ? ((totalSales - prevTotalSales) / prevTotalSales) * 100 : 0;

    // Customer calculations
    const totalCustomers = customers.length;
    const customersChange = 12; // Mock data

    // Low stock items
    const lowStockItems = inventory.filter(item => item.quantity <= item.minStock).length;

    // Chart data for last 7 days
    const chartData = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(today, 6 - i);
      const dayName = format(date, 'EEE');
      const daySales = sales.filter(s => 
        format(new Date(s.date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
      );
      const revenue = daySales.reduce((acc, s) => acc + s.totalAmount, 0);
      return { day: dayName, revenue, sales: daySales.length };
    });

    // Top products
    const productSales = salesLast30Days
      .flatMap(s => s.items)
      .reduce((acc, item) => {
        if (!acc[item.name]) {
          acc[item.name] = { name: item.name, value: 0, sales: 0 };
        }
        acc[item.name].value += item.total;
        acc[item.name].sales += item.quantity;
        return acc;
      }, {} as Record<string, { name: string; value: number; sales: number }>);

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.value - a.value)
      .slice(0, 5);

    // Recent sales
    const recentSales = sales.slice(-5).reverse();

    return {
      totalRevenue,
      revenueChange,
      totalSales,
      salesChange,
      totalCustomers,
      customersChange,
      lowStockItems,
      chartData,
      topProducts,
      recentSales
    };
  }, [sales, inventory, customers, isClient]);

  if (!isClient) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
    </div>;
  }

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            لوحة التحكم
          </h1>
          <p className="text-muted-foreground mt-2">نظرة شاملة على أداء صيدليتك</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-2">
            <Activity className="h-4 w-4" />
            مباشر
          </Badge>
          <Button className="gradient-bg hover-lift">
            <Sparkles className="h-4 w-4 ml-2" />
            تقرير شامل
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="gradient-card hover-lift border-0 shadow-modern">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle>
            <div className="p-2 bg-primary/10 rounded-lg">
              <DollarSign className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalRevenue.toLocaleString()} ر.س</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {dashboardData.revenueChange >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 ml-1" />
              )}
              <span className={dashboardData.revenueChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(dashboardData.revenueChange).toFixed(1)}%
              </span>
              <span className="mr-1">من الشهر الماضي</span>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card hover-lift border-0 shadow-modern">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المبيعات</CardTitle>
            <div className="p-2 bg-primary/10 rounded-lg">
              <ShoppingCart className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalSales}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {dashboardData.salesChange >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 ml-1" />
              )}
              <span className={dashboardData.salesChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(dashboardData.salesChange).toFixed(1)}%
              </span>
              <span className="mr-1">من الشهر الماضي</span>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card hover-lift border-0 shadow-modern">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">العملاء</CardTitle>
            <div className="p-2 bg-primary/10 rounded-lg">
              <Users className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalCustomers}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
              <span className="text-green-500">+{dashboardData.customersChange}%</span>
              <span className="mr-1">من الشهر الماضي</span>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card hover-lift border-0 shadow-modern">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تنبيهات المخزون</CardTitle>
            <div className="p-2 bg-orange-500/10 rounded-lg">
              <Package className="h-4 w-4 text-orange-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.lowStockItems}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Target className="h-4 w-4 text-orange-500 ml-1" />
              <span className="text-orange-500">يحتاج إعادة تخزين</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Revenue Chart */}
        <Card className="gradient-card border-0 shadow-modern-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              الإيرادات الأسبوعية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={dashboardData.chartData}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="day" stroke="#888" />
                <YAxis stroke="#888" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'white', 
                    border: 'none', 
                    borderRadius: '12px',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  }} 
                />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#8B5CF6" 
                  strokeWidth={3}
                  fillOpacity={1} 
                  fill="url(#colorRevenue)" 
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Products Chart */}
        <Card className="gradient-card border-0 shadow-modern-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              أفضل المنتجات مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={dashboardData.topProducts}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {dashboardData.topProducts.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'white', 
                    border: 'none', 
                    borderRadius: '12px',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  }} 
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {dashboardData.topProducts.slice(0, 3).map((product, index) => (
                <div key={product.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: COLORS[index] }}
                    />
                    <span className="text-sm">{product.name}</span>
                  </div>
                  <span className="text-sm font-medium">{product.value.toLocaleString()} ر.س</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
