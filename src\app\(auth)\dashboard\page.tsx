
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { BarChart, Package, DollarSign, AlertTriangle, ArrowUpRight } from "lucide-react";
import Link from 'next/link';
import { PageHeader } from "@/components/page-header";
import { Badge } from "@/components/ui/badge";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { <PERSON>, <PERSON><PERSON>hart as RechartsBarChart, CartesianGrid, XAxis, YAxis } from "recharts"
import { useState, useEffect, useMemo } from "react";
import { useStore } from "@/store/erp-store";
import { differenceInDays, parseISO, format, subDays, startOfMonth } from 'date-fns';
import { Skeleton } from "@/components/ui/skeleton";

export default function DashboardPage() {
  const { sales, inventory } = useStore();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const {
    totalRevenue,
    revenueChange,
    topSellingProduct,
    totalSales,
    salesChange,
    expirationAlerts,
    topSellingDrugs,
    chartData
  } = useMemo(() => {
    if (!isClient) {
      return { totalRevenue: 0, revenueChange: 0, topSellingProduct: { name: "N/A", sales: 0 }, totalSales: 0, salesChange: 0, expirationAlerts: [], topSellingDrugs: [], chartData: [] };
    }

    const today = new Date();
    const last30Days = subDays(today, 30);
    const last60Days = subDays(today, 60);

    const salesLast30Days = sales.filter(s => new Date(s.date) >= last30Days);
    const salesBeforeLast30Days = sales.filter(s => new Date(s.date) >= last60Days && new Date(s.date) < last30Days);

    const totalRevenue = salesLast30Days.reduce((acc, s) => acc + s.totalAmount, 0);
    const prevTotalRevenue = salesBeforeLast30Days.reduce((acc, s) => acc + s.totalAmount, 0);
    const revenueChange = prevTotalRevenue > 0 ? ((totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100 : totalRevenue > 0 ? 100 : 0;

    const totalSales = salesLast30Days.length;
    const prevTotalSales = salesBeforeLast30Days.length;
    const salesChange = prevTotalSales > 0 ? ((totalSales - prevTotalSales) / prevTotalSales) * 100 : totalSales > 0 ? 100 : 0;

    const productSales = salesLast30Days
        .flatMap(s => s.items)
        .reduce((acc, item) => {
            if (!acc[item.name]) {
                acc[item.name] = { sales: 0, revenue: 0 };
            }
            acc[item.name].sales += item.quantity;
            acc[item.name].revenue += item.total;
            return acc;
        }, {} as Record<string, { sales: number; revenue: number }>);

    const sortedProducts = Object.entries(productSales).sort(([, a], [, b]) => b.sales - a.sales);
    const topSellingProduct = sortedProducts.length > 0 ? { name: sortedProducts[0][0], ...sortedProducts[0][1] } : { name: "N/A", sales: 0 };
    const topSellingDrugs = sortedProducts.slice(0, 5).map(([name, data]) => ({ name, ...data }));

    const expirationAlerts = inventory.filter(item => {
        const expDate = parseISO(item.expDate);
        const daysToExpiry = differenceInDays(expDate, today);
        return daysToExpiry > 0 && daysToExpiry <= 60;
    }).slice(0, 3);
    
     const salesByMonth = sales.reduce((acc, s) => {
        const month = format(new Date(s.date), 'yyyy-MM');
        if (!acc[month]) {
            acc[month] = 0;
        }
        acc[month] += s.totalAmount;
        return acc;
    }, {} as Record<string, number>);

    const chartData = Object.entries(salesByMonth).map(([month, total]) => ({
        month: format(new Date(month), 'MMM'),
        total
    })).slice(-6);

    return { totalRevenue, revenueChange, topSellingProduct, totalSales, salesChange, expirationAlerts, topSellingDrugs, chartData };
  }, [sales, inventory, isClient]);

  if (!isClient) {
    return (
        <>
            <PageHeader title="لوحة التحكم" description="نظرة عامة على أداء صيدليتك." />
             <Skeleton className="h-48 mb-8" />
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
                <Skeleton className="h-28" />
                <Skeleton className="h-28" />
                <Skeleton className="h-28" />
                <Skeleton className="h-28" />
            </div>
             <div className="grid gap-8 grid-cols-1 xl:grid-cols-2">
                <Skeleton className="h-80" />
                <Skeleton className="h-80" />
             </div>
        </>
    );
  }

  return (
    <>
      <PageHeader
        title="لوحة التحكم"
        description="نظرة عامة على أداء صيدليتك."
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الإيرادات (آخر 30 يوم)</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRevenue.toLocaleString('ar-IQ')} د.ع</div>
            <p className={`text-xs ${revenueChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {revenueChange.toFixed(1)}%
                {revenueChange >= 0 ? ' +' : ' '} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المنتج الأكثر مبيعًا</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topSellingProduct.name}</div>
            <p className="text-xs text-muted-foreground">{topSellingProduct.sales.toLocaleString()} وحدة مباعة</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المبيعات (آخر 30 يوم)</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+{totalSales.toLocaleString()} فاتورة</div>
             <p className={`text-xs ${salesChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {salesChange.toFixed(1)}%
                {salesChange >= 0 ? ' +' : ' '} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تنبيهات انتهاء الصلاحية</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{expirationAlerts.length} تنبيهات</div>
            <p className="text-xs text-muted-foreground">منتجات تنتهي صلاحيتها قريبًا</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-8 grid-cols-1 xl:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>نظرة عامة على المبيعات</CardTitle>
            <CardDescription>ملخص لمبيعاتك خلال الـ 6 أشهر الماضية.</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
             <ChartContainer config={{ total: { label: "المبيعات", color: "hsl(var(--chart-1))" } }} className="h-[300px] w-full">
                <RechartsBarChart accessibilityLayer data={chartData}>
                  <CartesianGrid vertical={false} />
                   <XAxis
                    dataKey="month"
                    tickLine={false}
                    tickMargin={10}
                    axisLine={false}
                    tickFormatter={(value) => value.slice(0, 3)}
                  />
                  <YAxis />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent indicator="dot" />}
                  />
                  <Bar dataKey="total" fill="var(--color-total)" radius={8} />
                </RechartsBarChart>
              </ChartContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center">
             <div className="grid gap-2">
                <CardTitle>الأدوية الأكثر مبيعًا</CardTitle>
                <CardDescription>المنتجات الأكثر رواجًا هذا الشهر.</CardDescription>
             </div>
             <Button asChild size="sm" className="mr-auto gap-1">
                <Link href="/reports">
                   عرض الكل
                   <ArrowUpRight className="h-4 w-4" />
                </Link>
             </Button>
          </CardHeader>
          <CardContent>
             <div className="overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>اسم الدواء</TableHead>
                            <TableHead className="text-right">المبيعات</TableHead>
                            <TableHead className="text-right">الإيرادات</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {topSellingDrugs.map((drug) => (
                            <TableRow key={drug.name}>
                                <TableCell><div className="font-medium">{drug.name}</div></TableCell>
                                <TableCell className="text-right">{drug.sales}</TableCell>
                                <TableCell className="text-right">{drug.revenue.toLocaleString()} د.ع</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
             </div>
          </CardContent>
        </Card>
      </div>

       <Card className="mt-8">
          <CardHeader>
             <CardTitle>تنبيهات انتهاء الصلاحية</CardTitle>
             <CardDescription>
                الأدوية التي تقترب من تاريخ انتهاء صلاحيتها.
             </CardDescription>
          </CardHeader>
          <CardContent>
             <div className="overflow-x-auto">
                 <Table>
                    <TableHeader>
                       <TableRow>
                          <TableHead>اسم الدواء</TableHead>
                          <TableHead>معرف الدفعة</TableHead>
                          <TableHead className="text-right">تنتهي الصلاحية</TableHead>
                       </TableRow>
                    </TableHeader>
                    <TableBody>
                       {expirationAlerts.map((drug) => (
                       <TableRow key={`${drug.id}-${drug.batchNumber}`}>
                          <TableCell>{drug.name}</TableCell>
                          <TableCell>{drug.batchNumber}</TableCell>
                          <TableCell className="text-right">
                             <Badge variant="destructive">خلال {differenceInDays(parseISO(drug.expDate), new Date())} يومًا</Badge>
                          </TableCell>
                       </TableRow>
                       ))}
                    </TableBody>
                 </Table>
             </div>
          </CardContent>
       </Card>
    </>
  );
}
