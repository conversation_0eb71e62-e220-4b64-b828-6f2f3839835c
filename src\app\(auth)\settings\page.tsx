
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from '@/hooks/use-toast';
import { useStore } from '@/store/erp-store';
import { CheckCircle, Users, ArrowRight, ShieldCheck, Sparkles, Loader2, Info, DatabaseBackup, ListCollapse } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BackupRestore } from '@/components/settings/backup-restore';


export default function SettingsPage() {
    const { 
        settings, 
        updateSettings, 
        setSystemLogo, 
        setPrintLogo 
    } = useStore();
    const { toast } = useToast();
    
    const [companyInfo, setCompanyInfo] = useState({
        name: settings.companyInfo.name,
        description: settings.companyInfo.description,
        address: settings.companyInfo.address,
        invoiceNotes: settings.invoiceNotes,
    });
    
    const handleInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setCompanyInfo(prev => ({...prev, [name]: value}));
    }

    const handleSaveChanges = () => {
        updateSettings({
            companyInfo: {
                name: companyInfo.name,
                description: companyInfo.description,
                address: companyInfo.address,
            },
            invoiceNotes: companyInfo.invoiceNotes,
        });
        toast({
            title: "تم الحفظ بنجاح",
            description: "تم تحديث إعدادات الشركة بنجاح.",
            action: <CheckCircle className="h-5 w-5 text-green-500" />,
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, setter: (svg: string) => void, toastTitle: string) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type === 'image/svg+xml') {
                const reader = new FileReader();
                reader.onload = (evt) => {
                    const svgContent = evt.target?.result as string;
                    setter(svgContent);
                    toast({ title: toastTitle, description: "تم تحديث الشعار بنجاح." });
                };
                reader.readAsText(file);
            } else {
                toast({ variant: 'destructive', title: 'ملف غير صالح', description: 'الرجاء تحميل ملف بصيغة SVG فقط.' });
            }
        }
    };

  return (
    <>
      <PageHeader
        title="الإعدادات"
        description="تكوين الإعدادات العامة، إدارة المستخدمين، وتخصيص مظهر النظام."
      />
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">الإعدادات العامة</TabsTrigger>
            <TabsTrigger value="users">إدارة المستخدمين</TabsTrigger>
            <TabsTrigger value="roles">الأدوار والصلاحيات</TabsTrigger>
            <TabsTrigger value="activityLog">سجل الأنشطة</TabsTrigger>
            <TabsTrigger value="backup">النسخ الاحتياطي</TabsTrigger>
        </TabsList>
        <TabsContent value="general">
             <Card className="mt-6">
                <CardHeader>
                <CardTitle>معلومات الشركة</CardTitle>
                <CardDescription>
                    تحديث معلومات شركتك وتفاصيل الفاتورة.
                </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                <div className="space-y-2">
                    <Label htmlFor="company-name">اسم الشركة</Label>
                    <Input 
                        id="company-name"
                        name="name" 
                        value={companyInfo.name} 
                        onChange={handleInfoChange} 
                    />
                </div>
                 <div className="space-y-2">
                    <Label htmlFor="company-description">وصف الشركة (للطباعة)</Label>
                    <Input 
                        id="company-description"
                        name="description" 
                        value={companyInfo.description} 
                        onChange={handleInfoChange}
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="address">العنوان والهاتف</Label>
                    <Textarea 
                        id="address" 
                        name="address"
                        placeholder="أدخل عنوان شركتك ورقم هاتفك" 
                        value={companyInfo.address}
                        onChange={handleInfoChange}
                        rows={3}
                    />
                </div>
                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <Label htmlFor="invoice-notes">ملاحظات تذييل الفاتورة</Label>
                    </div>
                    <Textarea 
                        id="invoice-notes" 
                        name="invoiceNotes"
                        placeholder="أدخل الملاحظات التي ستظهر في الفواتير، مثل شروط الدفع." 
                        value={companyInfo.invoiceNotes}
                        onChange={handleInfoChange}
                        rows={3}
                    />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <Label htmlFor="logo">شعار النظام (ملون)</Label>
                        <Input id="logo" type="file" accept="image/svg+xml" onChange={(e) => handleFileChange(e, setSystemLogo, "تم تغيير شعار النظام")} />
                    </div>
                     <div className="space-y-2">
                        <Label htmlFor="print-logo">شعار الطباعة (يفضل أسود)</Label>
                        <Input id="print-logo" type="file" accept="image/svg+xml" onChange={(e) => handleFileChange(e, setPrintLogo, "تم تغيير شعار الطباعة")} />
                    </div>
                </div>
                 <div className="flex items-start gap-2 p-3 rounded-md bg-muted/50 text-muted-foreground text-xs">
                    <Info className="h-4 w-4 shrink-0 mt-0.5"/>
                    <span>
                        لأفضل النتائج، يرجى استخدام شعار بصيغة SVG. يمكن استخدام أدوات عبر الإنترنت لتحويل صور PNG أو JPG إلى SVG.
                    </span>
                 </div>
                </CardContent>
                <CardFooter>
                <Button onClick={handleSaveChanges}>حفظ التغييرات</Button>
                </CardFooter>
            </Card>
        </TabsContent>
         <TabsContent value="users">
            <Card className="mt-6">
                <CardHeader>
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-full bg-primary/10">
                            <Users className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                            <CardTitle>إدارة المستخدمين</CardTitle>
                            <CardDescription>
                                إضافة مستخدمين جدد وتعديل حساباتهم وأدوارهم.
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">
                        هنا يمكنك التحكم الكامل في حسابات المستخدمين. يمكنك إنشاء حسابات جديدة للموظفين، وتعيين أدوار محددة لكل منهم (مثل: مدير، محاسب، بائع)، وتعديل صلاحياتهم أو إيقاف حساباتهم عند الحاجة.
                    </p>
                </CardContent>
                <CardFooter>
                    <Link href="/settings/users">
                       <Button>
                          <span>الانتقال إلى إدارة المستخدمين</span>
                          <ArrowRight className="mr-2 h-4 w-4" />
                       </Button>
                    </Link>
                </CardFooter>
            </Card>
        </TabsContent>
        <TabsContent value="roles">
            <Card className="mt-6">
                <CardHeader>
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-full bg-primary/10">
                            <ShieldCheck className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                            <CardTitle>إدارة الأدوار والصلاحيات</CardTitle>
                            <CardDescription>
                                تحديد صلاحيات تفصيلية (عرض، إضافة، تعديل، حذف) لكل دور.
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">
                        تتيح لك هذه الواجهة التحكم الدقيق في ما يمكن لكل دور (Role) أن يفعله داخل النظام. يمكنك منح أو حجب صلاحيات الوصول لكل صفحة أو وظيفة، مما يضمن أقصى درجات الأمان والتحكم.
                    </p>
                </CardContent>
                <CardFooter>
                    <Link href="/settings/roles">
                       <Button>
                          <span>الانتقال إلى إدارة الصلاحيات</span>
                          <ArrowRight className="mr-2 h-4 w-4" />
                       </Button>
                    </Link>
                </CardFooter>
            </Card>
        </TabsContent>
        <TabsContent value="activityLog">
            <Card className="mt-6">
                <CardHeader>
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-full bg-primary/10">
                            <ListCollapse className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                            <CardTitle>سجل أنشطة المستخدمين</CardTitle>
                            <CardDescription>
                                عرض وتدقيق جميع الإجراءات التي تمت في النظام.
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">
                        هنا يمكنك مراجعة سجل كامل لجميع الأنشطة التي قام بها المستخدمون، مثل إنشاء الفواتير، حذف العملاء، تغيير الإعدادات، والمزيد. هذه الأداة ضرورية للرقابة والأمان.
                    </p>
                </CardContent>
                <CardFooter>
                    <Link href="/settings/activity-log">
                       <Button>
                          <span>عرض سجل الأنشطة</span>
                          <ArrowRight className="mr-2 h-4 w-4" />
                       </Button>
                    </Link>
                </CardFooter>
            </Card>
        </TabsContent>
         <TabsContent value="backup">
            <div className="mt-6">
                <BackupRestore />
            </div>
        </TabsContent>
      </Tabs>
    </>
  );
}
