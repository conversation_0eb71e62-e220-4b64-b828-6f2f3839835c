
'use client';

import React from 'react';
import { Logo } from '@/components/icons';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Undo2, Receipt, Banknote, FileText } from 'lucide-react';


export interface ReportTemplateProps {
  title: string;
  dateRange: string;
  reportType: string;
  data: any;
  companyInfo: {
    name: string;
    description: string;
    address: string;
  };
  printLogoSvg: string | null;
}


export function ReportTemplate({
    title,
    dateRange,
    reportType,
    data,
    companyInfo,
    printLogoSvg
}: ReportTemplateProps) {
  
  const renderReportContent = () => {
    switch (reportType) {
      case 'sales':
      case 'purchases':
        return renderDetailedReport(data.rows, reportType);
      case 'account':
        return renderAccountStatement(data.rows, data.headers, data.summary);
      case 'item_movement':
        return renderSimpleTable(data.rows, data.headers);
      case 'cashbox':
        return renderCashboxReport(data);
      case 'profit_loss':
        return renderProfitLoss(data);
      default:
        return <p>نوع التقرير غير مدعوم للطباعة.</p>;
    }
  };

  const renderDetailedReport = (rows: any[], type: 'sales' | 'purchases') => (
    <div className="space-y-2">
      {rows.map((row) => (
        <div key={row.id} className="border p-2 rounded-md break-inside-avoid-page">
           <div className="grid grid-cols-4 w-full text-xs text-right mb-1">
                <span>{row.id}</span>
                <span>{row.date}</span>
                <span>{row.customerName || row.supplierName}</span>
                <span className="font-semibold text-left">{row.totalAmount.toLocaleString('ar-IQ')} د.ع</span>
            </div>
            <div className="px-2 py-1 bg-muted/30 rounded-sm">
                <Table>
                    <TableHeader>
                        <TableRow className="text-xs">
                            <TableHead className="h-6">المادة</TableHead>
                            <TableHead className="h-6">الكمية</TableHead>
                            <TableHead className="h-6">البونص</TableHead>
                            <TableHead className="h-6">السعر</TableHead>
                            <TableHead className="h-6 text-left">الإجمالي</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {row.items.map((item: any, index: number) => (
                        <TableRow key={index} className="text-xs">
                            <TableCell className="py-1">{item.name}</TableCell>
                            <TableCell className="py-1">{item.quantity}</TableCell>
                            <TableCell className="py-1">{item.bonus || 0}</TableCell>
                            <TableCell className="py-1">{item.price.toLocaleString('ar-IQ')} د.ع</TableCell>
                            <TableCell className="py-1 text-left">{item.total.toLocaleString('ar-IQ')} د.ع</TableCell>
                        </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
      ))}
    </div>
  );

  const getIconForType = (type: string) => {
    if (type?.includes('فاتورة')) return <Receipt className="h-4 w-4 text-blue-500" />;
    if (type?.includes('دفعة')) return <Banknote className="h-4 w-4 text-green-500" />;
    if (type?.includes('مرتجع')) return <Undo2 className="h-4 w-4 text-orange-500" />;
    return <FileText className="h-4 w-4 text-gray-500" />;
  };

  const renderAccountStatement = (rows: any[], headers: string[], summary: any) => {
    return (
     <div>
        {summary && (
            <div className="grid grid-cols-4 gap-2 mb-4 text-center text-xs">
                <div className="p-2 bg-gray-100 rounded-md">
                    <p className="font-semibold">رصيد بداية المدة</p>
                    <p className="font-bold">{(summary.openingBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-red-100 rounded-md">
                    <p className="font-semibold text-red-800">إجمالي المدين</p>
                    <p className="font-bold text-red-800">{(summary.totalDebit || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-green-100 rounded-md">
                    <p className="font-semibold text-green-800">إجمالي الدائن</p>
                    <p className="font-bold text-green-800">{(summary.totalCredit || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-gray-100 rounded-md">
                    <p className="font-semibold">الرصيد النهائي</p>
                    <p className="font-bold">{(summary.finalBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
            </div>
        )}
         <Table>
            <TableHeader>
                <TableRow>
                    {headers.map(h => <TableHead key={h}>{h}</TableHead>)}
                </TableRow>
            </TableHeader>
            <TableBody>
                 {rows.map((row: any, rowIndex: number) => (
                    <TableRow key={rowIndex}>
                        <TableCell>{row.date}</TableCell>
                        <TableCell>
                            <div className="flex items-center gap-2">
                                <span className="no-print">{getIconForType(row.type)}</span>
                                <span>{row.type || 'N/A'}</span>
                                  {row.status && row.isInvoice && (
                                    <Badge variant={row.status === 'paid' ? 'secondary' : row.status === 'partial' ? 'default' : 'destructive'} className={cn("no-print", row.status === 'paid' && "bg-green-100 text-green-800", row.status === 'partial' && "bg-yellow-100 text-yellow-800")}>
                                        {row.status === 'paid' ? 'مسددة' : row.status === 'partial' ? 'جزئية' : 'غير مسددة'}
                                    </Badge>
                                )}
                            </div>
                        </TableCell>
                        <TableCell className="text-muted-foreground">{row.description}</TableCell>
                        <TableCell className="font-mono text-red-600 text-center">{row.debit > 0 ? row.debit.toLocaleString('ar-IQ') : '-'}</TableCell>
                        <TableCell className="font-mono text-green-600 text-center">{row.credit > 0 ? row.credit.toLocaleString('ar-IQ') : '-'}</TableCell>
                        <TableCell className="font-mono text-right">{row.balance.toLocaleString('ar-IQ')}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
            {summary && (
                <tfoot className="border-t-2 font-bold">
                    <TableRow>
                        <TableCell colSpan={3}>الإجماليات</TableCell>
                        <TableCell className="font-mono text-red-600 text-center">{summary.totalDebit.toLocaleString('ar-IQ')}</TableCell>
                        <TableCell className="font-mono text-green-600 text-center">{summary.totalCredit.toLocaleString('ar-IQ')}</TableCell>
                        <TableCell className="font-mono text-right">{summary.finalBalance.toLocaleString('ar-IQ')}</TableCell>
                    </TableRow>
                </tfoot>
            )}
        </Table>
     </div>
  )};

  const renderSimpleTable = (rows: any[], headers: string[]) => (
     <Table>
        <TableHeader>
            <TableRow>
                {headers.map(h => <TableHead key={h}>{h}</TableHead>)}
            </TableRow>
        </TableHeader>
        <TableBody>
            {rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                    {Object.values(row).map((cell: any, cellIndex) => {
                         const isNumeric = ['quantityIn', 'quantityOut', 'balance', 'bonus'].includes(Object.keys(row)[cellIndex]);
                         return (
                            <TableCell key={cellIndex} className={cn(isNumeric && 'font-mono')}>
                                {isNumeric ? Number(cell).toLocaleString('ar-IQ') : cell}
                            </TableCell>
                         )
                    })}
                </TableRow>
            ))}
        </TableBody>
    </Table>
  );

  const renderCashboxReport = (reportData: any) => {
    const { rows, headers, summary } = reportData;
    if (!summary) return null;

    return (
        <div>
            <div className="grid grid-cols-4 gap-2 mb-4 text-center text-xs">
                <div className="p-2 bg-muted rounded-md">
                    <p className="text-muted-foreground">رصيد بداية المدة</p>
                    <p className="font-bold">{(summary.startBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-green-100 rounded-md">
                    <p className="text-green-700">إجمالي المقبوضات</p>
                    <p className="font-bold text-green-800">{(summary.totalIncome || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-red-100 rounded-md">
                    <p className="text-red-700">إجمالي المدفوعات</p>
                    <p className="font-bold text-red-800">{(summary.totalExpense || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
                <div className="p-2 bg-muted rounded-md">
                    <p className="text-muted-foreground">رصيد نهاية المدة</p>
                    <p className="font-bold">{(summary.endBalance || 0).toLocaleString('ar-IQ')} د.ع</p>
                </div>
            </div>
            <Table>
                <TableHeader><TableRow>{headers.map(h => <TableHead key={h}>{h}</TableHead>)}</TableRow></TableHeader>
                <TableBody>
                    {rows.map((row: any, index: number) => (
                        <TableRow key={index}>
                            <TableCell>{row.date}</TableCell>
                            <TableCell>{row.description}</TableCell>
                            <TableCell className="text-green-600 font-mono">{row.type === 'income' ? row.amount.toLocaleString('ar-IQ') : '0'}</TableCell>
                            <TableCell className="text-red-600 font-mono">{row.type === 'expense' ? row.amount.toLocaleString('ar-IQ') : '0'}</TableCell>
                            <TableCell className="font-mono">{row.balance.toLocaleString('ar-IQ')}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
  };


  const renderProfitLoss = (reportData: any) => {
    const { summary, expenses } = reportData;
    if (!summary) return null;

    return (
      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-4">
            <h3 className="text-base font-medium text-primary border-b pb-2">الملخص المالي</h3>
            <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                    <span>إجمالي إيرادات المبيعات</span>
                    <span className="font-semibold">{summary.totalRevenue.toLocaleString('ar-IQ')} د.ع</span>
                </div>
                <div className="flex justify-between">
                    <span>(-) تكلفة البضاعة المباعة (COGS)</span>
                    <span className="font-semibold">({summary.cogs.toLocaleString('ar-IQ')}) د.ع</span>
                </div>
                <div className="flex justify-between font-bold border-t pt-2">
                    <span>= هامش الربح الإجمالي</span>
                    <span className="text-green-600">{summary.grossProfit.toLocaleString('ar-IQ')} د.ع</span>
                </div>
                <div className="flex justify-between pt-4">
                    <span>(-) إجمالي المصاريف</span>
                    <span className="font-semibold">({summary.totalExpenses.toLocaleString('ar-IQ')}) د.ع</span>
                </div>
                    <div className="flex justify-between font-bold text-base border-t-2 border-primary pt-2 mt-2">
                    <span>= صافي الربح / الخسارة</span>
                    <span className={cn(summary.netProfit >= 0 ? 'text-green-700' : 'text-red-700')}>
                        {summary.netProfit.toLocaleString('ar-IQ')} د.ع
                    </span>
                </div>
            </div>
        </div>
        <div className="space-y-4">
            <h3 className="text-base font-medium text-primary border-b pb-2">تفصيل المصاريف</h3>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>البيان</TableHead>
                        <TableHead className="text-right">المبلغ</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {expenses.map((expense: any, index: number) => (
                        <TableRow key={index}>
                            <TableCell>{expense.description}</TableCell>
                            <TableCell className="text-right">{expense.amount.toLocaleString('ar-IQ')} د.ع</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
                    <tfoot className="border-t">
                    <TableRow>
                        <TableHead>الإجمالي</TableHead>
                        <TableHead className="text-right font-bold">{summary.totalExpenses.toLocaleString('ar-IQ')} د.ع</TableHead>
                    </TableRow>
                </tfoot>
            </Table>
        </div>
      </div>
    );
  };


  return (
    <div className="bg-card text-card-foreground p-4 sm:p-8 font-sans">
      <header className="flex flex-col sm:flex-row justify-between items-start pb-4 border-b-2 border-black gap-4">
        <div className="text-center sm:text-right flex-grow">
            <h1 className="text-xl sm:text-2xl font-bold text-primary">{companyInfo.name}</h1>
            <p className="text-xs sm:text-sm">{companyInfo.description}</p>
            <p className="text-xs sm:text-sm mt-2">{companyInfo.address}</p>
        </div>
        <div className="w-32 h-32 flex items-center justify-center self-center sm:self-start shrink-0">
           {printLogoSvg ? (
                <img 
                    src={`data:image/svg+xml;base64,${btoa(printLogoSvg)}`} 
                    alt="Company Logo"
                    className="w-full h-full object-contain"
                />
            ) : (
                <Logo className="w-full h-full text-primary" />
            )}
        </div>
      </header>

      <section className="text-center my-4 py-2">
        <h2 className="text-xl font-bold">{title}</h2>
        <p className="text-sm text-muted-foreground">{dateRange}</p>
      </section>

      <Separator className="my-4 bg-gray-400" />

      <main>
        {renderReportContent()}
      </main>

      <footer className="pt-8 mt-8 border-t-2 border-black text-xs">
          <div className="flex justify-around">
              <div className="text-center">
                  <p className="font-bold">--------------------</p>
                  <p>توقيع المدير</p>
              </div>
                <div className="text-center">
                  <p className="font-bold">--------------------</p>
                  <p>توقيع المحاسب</p>
              </div>
          </div>
          <div className="text-center mt-4">
            <p>تاريخ الطباعة: {new Date().toLocaleDateString('ar-IQ')} {new Date().toLocaleTimeString('ar-IQ')}</p>
          </div>
      </footer>
    </div>
  );
}
