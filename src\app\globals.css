
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', '<PERSON><PERSON><PERSON>', sans-serif;
}

@layer base {
  :root {
    --background: 220 20% 97%;
    --foreground: 220 15% 25%;
    --card: 0 0% 100%;
    --card-foreground: 220 15% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 25%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 15% 90%;
    --secondary-foreground: 220 15% 25%;
    --muted: 220 15% 90%;
    --muted-foreground: 220 10% 45%;
    --accent: 220 15% 93%;
    --accent-foreground: 220 15% 25%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 15% 88%;
    --input: 220 15% 92%;
    --ring: 217 91% 60%;
    --chart-1: 217 91% 60%;
    --chart-2: 217 71% 70%;
    --chart-3: 160 84% 40%;
    --chart-4: 280 84% 60%;
    --chart-5: 40 84% 60%;
    --radius: 0.5rem;
    --sidebar-background: 220 15% 15%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 15% 25%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 220 15% 25%;
    --sidebar-ring: 217 91% 60%;
  }
  .dark {
    --background: 220 15% 10%;
    --foreground: 0 0% 98%;
    --card: 220 15% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 220 15% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 15% 25%;
    --secondary-foreground: 0 0% 98%;
    --muted: 220 15% 25%;
    --muted-foreground: 220 10% 65%;
    --accent: 220 15% 20%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 15% 25%;
    --input: 220 15% 25%;
    --ring: 217 91% 60%;
    --chart-1: 217 91% 60%;
    --chart-2: 217 71% 70%;
    --chart-3: 160 84% 40%;
    --chart-4: 280 84% 60%;
    --chart-5: 40 84% 60%;
    --sidebar-background: 220 15% 10%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 15% 20%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 220 15% 25%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  @media print {
    html, body {
      height: auto;
      font-size: 10pt;
      background-color: #fff !important;
      color: #000 !important;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
    
    .no-print, .no-print * {
      display: none !important;
    }

    .print-area, .print-area * {
       visibility: visible;
    }
    .print-area {
       position: absolute;
       left: 0;
       top: 0;
       width: 100%;
       margin: 0;
       padding: 0;
    }

    .print-card {
        border: none !important;
        box-shadow: none !important;
    }

    .print-title {
        color: black !important;
    }
    
    .print-description {
        color: black !important;
    }
    
    .no-print-arrow svg {
        display: none !important;
    }

    @page {
      size: A4;
      margin: 15mm;
    }
  }
}
