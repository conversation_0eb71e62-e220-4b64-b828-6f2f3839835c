
"use client";

import { useState } from "react";
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import { ArrowLeft, UserPlus, Edit, KeyRound, Power, PowerOff, Trash2 } from "lucide-react";


// Mock data - in a real app, this would come from the store or an API
const mockUsers = [
  { id: 1, name: "علي حسن", username: "ali.hassan", role: "مدير", status: 'active' },
  { id: 2, name: "فاطمة أحمد", username: "fatima.ahmed", role: "محاسب", status: 'active' },
  { id: 3, name: "محمد كريم", username: "mohammed.karim", role: "بائع", status: 'inactive' },
];

const mockRoles = ["مدير", "محاسب", "بائع", "مدخل بيانات"]; // This would also come from the store/API

type User = typeof mockUsers[0];

const initialNewUserState = {
    name: '',
    username: '',
    password: '',
    role: 'بائع',
};

export default function UsersPage() {
    const [users, setUsers] = useState<User[]>(mockUsers);
    const [roles, setRoles] = useState<string[]>(mockRoles); // State for roles
    const [newUser, setNewUser] = useState(initialNewUserState);
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [userToResetPassword, setUserToResetPassword] = useState<User | null>(null);
    const [newPassword, setNewPassword] = useState('');
    
    const [isAddUserOpen, setIsAddUserOpen] = useState(false);
    const [isEditUserOpen, setIsEditUserOpen] = useState(false);
    const [isResetPasswordOpen, setIsResetPasswordOpen] = useState(false);

    const { toast } = useToast();

    // Handlers for Add User Dialog
    const handleAddUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    const handleAddUserRoleChange = (value: string) => {
        setNewUser(prev => ({...prev, role: value}));
    }

    const handleAddUserFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newUser.name || !newUser.username || !newUser.password) {
            toast({ variant: "destructive", title: "حقول مطلوبة", description: "يرجى ملء جميع الحقول المطلوبة." });
            return;
        }
        const newUser_data: User = {
            id: Date.now(),
            name: newUser.name,
            username: newUser.username,
            role: newUser.role,
            status: 'active' as const,
        };
        setUsers(prev => [newUser_data, ...prev]);
        toast({ title: "تمت الإضافة بنجاح", description: `تمت إضافة المستخدم "${newUser_data.name}" إلى القائمة.` });
        setNewUser(initialNewUserState);
        setIsAddUserOpen(false);
    };

    // Handlers for Edit User Dialog
    const openEditDialog = (user: User) => {
        setEditingUser(user);
        setIsEditUserOpen(true);
    };

    const handleEditUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!editingUser) return;
        const { name, value } = e.target;
        setEditingUser(prev => prev ? { ...prev, [name]: value } : null);
    };
    
    const handleEditUserRoleChange = (value: string) => {
        if (!editingUser) return;
        setEditingUser(prev => prev ? { ...prev, role: value } : null);
    }
    
    const handleEditUserFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingUser) return;

        setUsers(prev => prev.map(u => u.id === editingUser.id ? editingUser : u));
        toast({ title: "تم التعديل بنجاح", description: `تم تحديث بيانات المستخدم "${editingUser.name}".` });
        setIsEditUserOpen(false);
        setEditingUser(null);
    };


    // Handler for Password Reset
    const openResetPasswordDialog = (user: User) => {
        setUserToResetPassword(user);
        setIsResetPasswordOpen(true);
    };
    
    const handlePasswordReset = (e: React.FormEvent) => {
        e.preventDefault();
        if (!userToResetPassword || !newPassword) return;
        // In a real app, you would make an API call here.
        toast({ title: "تم تغيير كلمة المرور", description: `تم تعيين كلمة مرور جديدة للمستخدم ${userToResetPassword.name}.` });
        setIsResetPasswordOpen(false);
        setNewPassword('');
        setUserToResetPassword(null);
    }

    // Handler for User Status
    const toggleUserStatus = (user: User) => {
        const newStatus = user.status === 'active' ? 'inactive' : 'active';
        setUsers(prev => prev.map(u => u.id === user.id ? { ...u, status: newStatus } : u));
        toast({
            title: `تم تغيير حالة المستخدم`,
            description: `حالة المستخدم "${user.name}" الآن ${newStatus === 'active' ? 'نشط' : 'غير نشط'}.`
        });
    };
    
    // Handler for Delete
    const handleDeleteUser = (userId: number, userName: string) => {
        setUsers(prev => prev.filter(c => c.id !== userId));
        toast({ title: "تم الحذف بنجاح", description: `تم حذف المستخدم "${userName}" من القائمة.` });
    }

  return (
    <>
      <PageHeader
        title="إدارة المستخدمين"
        description="إضافة وتعديل وحذف المستخدمين وتحديد صلاحياتهم في النظام."
        action={
            <Button onClick={() => setIsAddUserOpen(true)}>
                <UserPlus className="ml-2 h-4 w-4" />
                إضافة مستخدم جديد
            </Button>
        }
      />
        <div className="mb-4">
            <Link href="/settings" className="inline-flex items-center text-sm text-primary hover:underline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                <span>العودة إلى الإعدادات</span>
            </Link>
        </div>
      <Card>
        <CardHeader>
           <CardTitle>قائمة المستخدمين</CardTitle>
           <CardDescription>
              عرض المستخدمين الحاليين وإدارة حساباتهم.
           </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم الكامل</TableHead>
                <TableHead>اسم المستخدم</TableHead>
                <TableHead>الدور</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead className="text-right">إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>
                     <Badge 
                        variant={user.status === 'active' ? 'secondary' : 'destructive'}
                        className={user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                     >
                        {user.status === 'active' ? 'نشط' : 'غير نشط'}
                     </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <AlertDialog>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">فتح القائمة</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openEditDialog(user)}><Edit className="ml-2 h-4 w-4" />تعديل البيانات</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openResetPasswordDialog(user)}><KeyRound className="ml-2 h-4 w-4" />تغيير كلمة المرور</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => toggleUserStatus(user)}>
                               {user.status === 'active' ? <PowerOff className="ml-2 h-4 w-4" /> : <Power className="ml-2 h-4 w-4" />}
                               {user.status === 'active' ? 'إيقاف الحساب' : 'تفعيل الحساب'}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialogTrigger asChild>
                                <DropdownMenuItem className="text-destructive"><Trash2 className="ml-2 h-4 w-4" />حذف المستخدم</DropdownMenuItem>
                            </AlertDialogTrigger>
                          </DropdownMenuContent>
                        </DropdownMenu>
                         <AlertDialogContent>
                            <AlertDialogHeader>
                            <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                            <AlertDialogDescription>
                                هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف حساب المستخدم "{user.name}" بشكل دائم.
                            </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteUser(user.id, user.name)}>متابعة</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Add User Dialog */}
      <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
        <DialogContent className="sm:max-w-md">
             <DialogHeader>
                <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleAddUserFormSubmit}>
                <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">الاسم الكامل</Label>
                        <Input id="name" name="name" value={newUser.name} onChange={handleAddUserInputChange} placeholder="مثال: أحمد علي" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="username">اسم المستخدم (للدخول)</Label>
                        <Input id="username" name="username" value={newUser.username} onChange={handleAddUserInputChange} placeholder="مثال: ahmed.ali" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="password">كلمة المرور</Label>
                        <Input id="password" name="password" type="password" value={newUser.password} onChange={handleAddUserInputChange} placeholder="••••••••" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="role">الدور (الصلاحية)</Label>
                         <Select value={newUser.role} onValueChange={handleAddUserRoleChange}>
                            <SelectTrigger id="role">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                {roles.map(role => (
                                    <SelectItem key={role} value={role}>{role}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <DialogFooter>
                    <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
                    <Button type="submit">حفظ المستخدم</Button>
                </DialogFooter>
            </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="sm:max-w-md">
             <DialogHeader>
                <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            </DialogHeader>
            {editingUser && (
                <form onSubmit={handleEditUserFormSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="edit-name">الاسم الكامل</Label>
                            <Input id="edit-name" name="name" value={editingUser.name} onChange={handleEditUserInputChange} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-username">اسم المستخدم (للدخول)</Label>
                            <Input id="edit-username" name="username" value={editingUser.username} onChange={handleEditUserInputChange} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-role">الدور (الصلاحية)</Label>
                             <Select 
                                value={editingUser.role} 
                                onValueChange={handleEditUserRoleChange}
                             >
                                <SelectTrigger id="edit-role"><SelectValue /></SelectTrigger>
                                <SelectContent>
                                    {roles.map(role => (
                                        <SelectItem key={role} value={role}>{role}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <DialogFooter>
                        <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
                        <Button type="submit">حفظ التغييرات</Button>
                    </DialogFooter>
                </form>
            )}
        </DialogContent>
      </Dialog>
      
      {/* Reset Password Dialog */}
      <Dialog open={isResetPasswordOpen} onOpenChange={setIsResetPasswordOpen}>
        <DialogContent className="sm:max-w-md">
             <DialogHeader>
                <DialogTitle>إعادة تعيين كلمة المرور</DialogTitle>
            </DialogHeader>
            {userToResetPassword && (
                <form onSubmit={handlePasswordReset}>
                    <div className="grid gap-4 py-4">
                        <p>أنت على وشك تعيين كلمة مرور جديدة للمستخدم: <span className="font-bold">{userToResetPassword.name}</span></p>
                        <div className="space-y-2">
                            <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
                            <Input id="new-password" name="newPassword" type="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} placeholder="••••••••" />
                        </div>
                    </div>
                    <DialogFooter>
                        <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
                        <Button type="submit">حفظ كلمة المرور</Button>
                    </DialogFooter>
                </form>
            )}
        </DialogContent>
      </Dialog>
    </>
  );
}

