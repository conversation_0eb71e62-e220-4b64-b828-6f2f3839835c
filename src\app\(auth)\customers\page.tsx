
"use client";

import { useState } from "react";
import { useRouter } from 'next/navigation';
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, PlusCircle, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useStore } from "@/store/erp-store";
import type { Customer } from "@/store/erp-store";


const initialNewCustomerState = {
    name: '',
    phone: '',
    address: '',
};

export default function CustomersPage() {
    const { 
        customers, 
        sales,
        addCustomer, 
        deleteCustomer, 
        addPayment,
    } = useStore();
    const router = useRouter();

    const [newCustomer, setNewCustomer] = useState(initialNewCustomerState);
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
    const [paymentAmount, setPaymentAmount] = useState(0);
    const { toast } = useToast();

    const viewAccountStatement = (customer: Customer) => {
        const query = new URLSearchParams({
            reportType: 'account',
            accountId: `customer-${customer.id}`
        }).toString();
        router.push(`/reports?${query}`);
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setNewCustomer(prev => ({ ...prev, [name]: value }));
    };

    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newCustomer.name || !newCustomer.phone) {
            toast({
                variant: "destructive",
                title: "حقول مطلوبة",
                description: "يرجى إدخال اسم العميل ورقم هاتفه على الأقل."
            });
            return;
        }

        const newCustomerData = {
            id: Date.now(),
            ...newCustomer,
            balance: 0,
        };

        addCustomer(newCustomerData);
        toast({
            title: "تمت الإضافة بنجاح",
            description: `تمت إضافة العميل "${newCustomerData.name}" إلى القائمة.`
        });

        setNewCustomer(initialNewCustomerState);
        setIsAddDialogOpen(false);
    };

    const handleDelete = (customerId: number, customerName: string) => {
        deleteCustomer(customerId);
        toast({
            title: "تم الحذف بنجاح",
            description: `تم حذف العميل "${customerName}" من القائمة.`
        });
    }

    const openPaymentDialog = (customer: Customer) => {
        setSelectedCustomer(customer);
        setIsPaymentDialogOpen(true);
        setPaymentAmount(0);
    };

    const handlePaymentSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedCustomer || paymentAmount <= 0) {
            toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء إدخال مبلغ صحيح.' });
            return;
        }

        addPayment({
            partyType: 'customer',
            partyId: selectedCustomer.id,
            partyName: selectedCustomer.name,
            amount: paymentAmount,
        });

        toast({
            title: 'تم تسجيل الدفعة',
            description: `تم تسجيل دفعة بقيمة ${paymentAmount.toLocaleString()} د.ع من العميل ${selectedCustomer.name}. تم تحديث صندوق النقد والفواتير.`
        });

        setIsPaymentDialogOpen(false);
        setSelectedCustomer(null);
    };

  return (
    <>
      <PageHeader
        title="إدارة العملاء"
        description="عرض وتعديل بيانات العملاء وكشوفات حساباتهم."
        action={
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                    <Button>
                        <PlusCircle className="ml-2 h-4 w-4" />
                        إضافة عميل جديد
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                     <DialogHeader>
                        <DialogTitle>إضافة عميل جديد</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleFormSubmit}>
                        <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">اسم العميل</Label>
                                <Input id="name" name="name" value={newCustomer.name} onChange={handleInputChange} placeholder="مثال: صيدلية التفاح الأخضر" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phone">رقم الهاتف</Label>
                                <Input id="phone" name="phone" value={newCustomer.phone} onChange={handleInputChange} placeholder="07XX XXX XXXX" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="address">العنوان</Label>
                                <Input id="address" name="address" value={newCustomer.address} onChange={handleInputChange} placeholder="مثال: بغداد - زيونة" />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">إلغاء</Button>
                            </DialogClose>
                            <Button type="submit">حفظ العميل</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        }
      />
      <Card>
        <CardHeader>
           <CardTitle>قائمة العملاء</CardTitle>
           <CardDescription>
              ابحث عن عميل أو قم بإدارة العملاء الحاليين.
           </CardDescription>
           <div className="pt-4">
              <Input placeholder="بحث عن عميل..." />
           </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>اسم العميل</TableHead>
                <TableHead>رقم الهاتف</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead>الرصيد</TableHead>
                <TableHead className="text-right">إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell>{customer.address}</TableCell>
                  <TableCell>
                     <Badge 
                        variant={customer.balance > 0 ? 'destructive' : customer.balance < 0 ? 'secondary' : 'default'}
                     >
                        {customer.balance.toLocaleString('ar-IQ')} د.ع
                     </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <AlertDialog>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">فتح القائمة</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
                            <DropdownMenuItem onSelect={() => viewAccountStatement(customer)}>عرض الكشف</DropdownMenuItem>
                            <DropdownMenuItem>تعديل البيانات</DropdownMenuItem>
                            <DropdownMenuItem onSelect={() => openPaymentDialog(customer)}>تسديد دفعة</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialogTrigger asChild>
                                <DropdownMenuItem className="text-destructive">حذف العميل</DropdownMenuItem>
                            </AlertDialogTrigger>
                          </DropdownMenuContent>
                        </DropdownMenu>
                         <AlertDialogContent>
                            <AlertDialogHeader>
                            <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                            <AlertDialogDescription>
                                هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف بيانات العميل "{customer.name}" بشكل دائم.
                            </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(customer.id, customer.name)}>متابعة</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Payment Dialog */}
        <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>تسجيل دفعة من عميل</DialogTitle>
                </DialogHeader>
                {selectedCustomer && (
                    <form onSubmit={handlePaymentSubmit}>
                        <div className="grid gap-4 py-4">
                            <p>تسجيل دفعة من العميل: <span className="font-bold">{selectedCustomer.name}</span></p>
                             <p>الرصيد الحالي: <span className="font-bold">{selectedCustomer.balance.toLocaleString('ar-IQ')} د.ع</span></p>
                            <div className="space-y-2">
                                <Label htmlFor="payment-amount">مبلغ الدفعة</Label>
                                <Input
                                    id="payment-amount"
                                    type="number"
                                    value={paymentAmount || ''}
                                    onChange={(e) => setPaymentAmount(Number(e.target.value))}
                                    placeholder="أدخل المبلغ"
                                    required
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">إلغاء</Button>
                            </DialogClose>
                            <Button type="submit">حفظ الدفعة</Button>
                        </DialogFooter>
                    </form>
                )}
            </DialogContent>
        </Dialog>
    </>
  );
}
