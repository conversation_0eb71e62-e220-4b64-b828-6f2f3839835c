
"use client";

import { useState } from "react";
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { useStore } from "@/store/erp-store";
import { ArrowLeft, User, Clock, ShieldAlert } from "lucide-react";
import Link from 'next/link';
import { format } from 'date-fns';

export default function ActivityLogPage() {
    const { activityLog } = useStore();
    const [searchTerm, setSearchTerm] = useState("");

    const filteredLog = activityLog.filter(log => 
        log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <>
            <PageHeader
                title="سجل أنشطة المستخدمين"
                description="عرض وتدقيق جميع الإجراءات التي تمت في النظام لضمان الأمان والمراقبة."
            />
            <div className="mb-4">
                <Link href="/settings" className="inline-flex items-center text-sm text-primary hover:underline">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    <span>العودة إلى الإعدادات</span>
                </Link>
            </div>
            <Card>
                <CardHeader>
                    <CardTitle>سجل التدقيق</CardTitle>
                    <CardDescription>
                        ابحث في السجل لتتبع أنشطة محددة.
                    </CardDescription>
                    <div className="pt-4">
                        <Input 
                            placeholder="بحث في السجل..." 
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[200px]">الوقت والتاريخ</TableHead>
                                <TableHead>المستخدم</TableHead>
                                <TableHead>الإجراء</TableHead>
                                <TableHead>التفاصيل</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredLog.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={4} className="h-24 text-center">
                                        لا توجد سجلات تطابق بحثك.
                                    </TableCell>
                                </TableRow>
                            ) : (
                                filteredLog.map((log) => (
                                    <TableRow key={log.id}>
                                        <TableCell>
                                            <div className="flex items-center gap-2 text-muted-foreground">
                                                <Clock className="h-4 w-4" />
                                                <span>{format(new Date(log.timestamp), 'yyyy/MM/dd, h:mm:ss a')}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <User className="h-4 w-4" />
                                                <span className="font-medium">{log.user}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <ShieldAlert className="h-4 w-4" />
                                                <span>{log.action}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-muted-foreground">{log.details}</TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    );
}

    