
"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PlusCircle, Save, Trash2, History, Sparkles, Loader2, Printer } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { createRoot } from "react-dom/client";
import { InvoiceTemplate, type InvoiceTemplateProps } from "@/components/invoice/invoice-template";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { useStore } from "@/store/erp-store";
import type { PurchaseItem, PurchaseRecord, Supplier, Product } from "@/store/erp-store";


export default function PurchasesPage() {
  const {
    suppliers,
    inventory,
    purchases,
    settings,
    addPurchase,
    deletePurchase,
    lastPurchaseInvoiceNumber,
  } = useStore();

  const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([]);
  const [supplierName, setSupplierName] = useState("");
  const [supplierId, setSupplierId] = useState<number | null>(null);
  const [discount, setDiscount] = useState(0);
  const [notes, setNotes] = useState(settings.invoiceNotes);
  
  // State for the item input form
  const [itemName, setItemName] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [bonus, setBonus] = useState(0);
  const [purchasePrice, setPurchasePrice] = useState(0);
  const [sellingPrice, setSellingPrice] = useState(0);
  const [expDate, setExpDate] = useState("");
  const [batchNumber, setBatchNumber] = useState("");

  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [issueDate, setIssueDate] = useState('');
  const [historySearchTerm, setHistorySearchTerm] = useState("");
  const { toast } = useToast();
  
  const [productSuggestions, setProductSuggestions] = useState<Product[]>([]);
  const [showProductSuggestions, setShowProductSuggestions] = useState(false);
  const itemNameInputRef = useRef<HTMLInputElement>(null);
  const productSuggestionsRef = useRef<HTMLDivElement>(null);

  const [supplierSuggestions, setSupplierSuggestions] = useState<Supplier[]>([]);
  const [showSupplierSuggestions, setShowSupplierSuggestions] = useState(false);
  const supplierNameInputRef = useRef<HTMLInputElement>(null);
  const supplierSuggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const generateInvoiceDetails = () => {
        setInvoiceNumber((lastPurchaseInvoiceNumber + 1).toString());
        setIssueDate(new Date().toISOString().split('T')[0]);
    };
    if (!invoiceNumber) {
        generateInvoiceDetails();
    }
  }, [invoiceNumber, lastPurchaseInvoiceNumber]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        itemNameInputRef.current &&
        !itemNameInputRef.current.contains(event.target as Node) &&
        productSuggestionsRef.current &&
        !productSuggestionsRef.current.contains(event.target as Node)
      ) {
        setShowProductSuggestions(false);
      }
      if (
        supplierNameInputRef.current &&
        !supplierNameInputRef.current.contains(event.target as Node) &&
        supplierSuggestionsRef.current &&
        !supplierSuggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSupplierSuggestions(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleItemNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setItemName(value);
    if (value) {
      const filteredSuggestions = inventory.filter(p => p.name.toLowerCase().includes(value.toLowerCase()));
      setProductSuggestions(filteredSuggestions);
      setShowProductSuggestions(true);
    } else {
      setProductSuggestions([]);
      setShowProductSuggestions(false);
      setPurchasePrice(0);
      setSellingPrice(0);
    }
  };

  const handleProductSuggestionClick = (product: Product) => {
    setItemName(product.name);
    setPurchasePrice(product.purchasePrice);
    setSellingPrice(product.sellingPrice);
    setProductSuggestions([]);
    setShowProductSuggestions(false);
  };

  const handleSupplierNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSupplierName(value);
    setSupplierId(null);
    if (value) {
      const filteredSuggestions = suppliers.filter(s => s.name.toLowerCase().includes(value.toLowerCase()));
      setSupplierSuggestions(filteredSuggestions);
      setShowSupplierSuggestions(true);
    } else {
      setSupplierSuggestions([]);
      setShowSupplierSuggestions(false);
    }
  };

  const handleSupplierSuggestionClick = (supplier: Supplier) => {
    setSupplierName(supplier.name);
    setSupplierId(supplier.id);
    setSupplierSuggestions([]);
    setShowSupplierSuggestions(false);
  };


  const handleAddItem = () => {
    if (!itemName || quantity <= 0 || purchasePrice <= 0 || !expDate || !batchNumber) {
        toast({
            variant: "destructive",
            title: "حقول مطلوبة",
            description: "الرجاء ملء جميع حقول المادة قبل الإضافة.",
        });
        return;
    }

    const newItem: PurchaseItem = {
      id: Date.now(), // Using timestamp for unique ID
      name: itemName,
      quantity,
      bonus,
      price: purchasePrice,
      sellingPrice,
      expDate,
      batchNumber,
      total: quantity * purchasePrice,
    };
    
    setPurchaseItems(prevItems => [...prevItems, newItem]);
    
    // Reset form
    setItemName("");
    setQuantity(1);
    setBonus(0);
    setPurchasePrice(0);
    setSellingPrice(0);
    setExpDate("");
    setBatchNumber("");
    setShowProductSuggestions(false);
    itemNameInputRef.current?.focus();
  };
  
  const handleRemoveItem = (id: number) => {
    setPurchaseItems(prevItems => prevItems.filter(item => item.id !== id));
  };

    const handlePrint = (data: InvoiceTemplateProps) => {
        const printWindow = window.open('', '_blank', 'left=50,top=50,width=1000,height=800');
        if (!printWindow) return;

        const printDocument = printWindow.document;
        printDocument.write('<html><head><title>طباعة فاتورة</title>');
        
        const styleSheets = Array.from(document.styleSheets);
        styleSheets.forEach(ss => {
            if (ss.href) {
                const link = printDocument.createElement('link');
                link.rel = 'stylesheet';
                link.href = ss.href;
                printDocument.head.appendChild(link);
            } else if (ss.ownerNode instanceof HTMLStyleElement) {
                 const style = printDocument.createElement('style');
                 style.textContent = ss.ownerNode.innerHTML;
                 printDocument.head.appendChild(style);
            }
        });

        const waitForStyles = new Promise<void>((resolve) => {
            let loadedCount = 0;
            const links = printDocument.head.getElementsByTagName('link');
            if (links.length === 0) { resolve(); return; }
            for (let i = 0; i < links.length; i++) {
                links[i].onload = () => { if (++loadedCount === links.length) resolve(); };
                links[i].onerror = () => { if (++loadedCount === links.length) resolve(); };
            }
        });

        waitForStyles.then(() => {
            printDocument.write('<style>@media print { @page { size: A4; margin: 0; } body { -webkit-print-color-adjust: exact; print-color-adjust: exact; margin: 1.6cm; } } </style>');
            printDocument.write('</head><body dir="rtl"></body></html>');
            printDocument.close(); 
            
            const printContentEl = printDocument.createElement('div');
            printDocument.body.appendChild(printContentEl);
            
            const root = createRoot(printContentEl);
            root.render(<InvoiceTemplate {...data} />);
            
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }, 500); 
        });
    }

  const handleDelete = (invoiceId: string) => {
    deletePurchase(invoiceId);
    toast({ title: 'تم الحذف', description: `تم حذف الفاتورة رقم ${invoiceId} بنجاح.` });
  };

  const handleSavePurchase = () => {
    if (!supplierName || !supplierId) {
        toast({
            variant: "destructive",
            title: "حقل مطلوب",
            description: "الرجاء اختيار مورد من القائمة.",
        });
        return;
    }
    if (purchaseItems.length === 0) {
        toast({
            variant: "destructive",
            title: "الفاتورة فارغة",
            description: "الرجاء إضافة مادة واحدة على الأقل."
        });
        return;
    }

    const subtotal = purchaseItems.reduce((acc, item) => acc + item.total, 0);
    const totalAmount = subtotal - discount;

    const newPurchaseRecord: Omit<PurchaseRecord, 'status' | 'paidAmount'> = {
        id: invoiceNumber,
        supplierId: supplierId,
        supplierName: supplierName,
        date: issueDate,
        items: purchaseItems,
        discount,
        notes,
        totalAmount
    };

    addPurchase(newPurchaseRecord);

    toast({
        title: "تم الحفظ بنجاح",
        description: `تم حفظ فاتورة الشراء وتحديث المخزون وحساب المورد.`,
    });
    
     const printData: InvoiceTemplateProps = {
        invoiceNumber: newPurchaseRecord.id,
        issueDate: newPurchaseRecord.date,
        invoiceType: 'فاتورة شراء',
        paymentMethod: 'آجل',
        region: '',
        customerName: newPurchaseRecord.supplierName,
        items: newPurchaseRecord.items.map((item, index) => ({
            seq: index + 1,
            name: item.name,
            quantity: item.quantity,
            bonus: item.bonus,
            price: item.price,
            exp: item.expDate,
            notes: `رقم الوجبة: ${item.batchNumber}`
        })),
        discount: newPurchaseRecord.discount,
        notes: newPurchaseRecord.notes,
        companyInfo: settings.companyInfo,
        printLogoSvg: settings.printLogoSvg,
     };

    handlePrint(printData);
    handleNewPurchase();
  };
  
  const handleNewPurchase = () => {
    setPurchaseItems([]);
    setSupplierName("");
    setSupplierId(null);
    setDiscount(0);
    setNotes(settings.invoiceNotes);
    setItemName("");
    setQuantity(1);
    setBonus(0);
    setPurchasePrice(0);
    setSellingPrice(0);
    setExpDate("");
    setBatchNumber("");
    setShowProductSuggestions(false);
    setShowSupplierSuggestions(false);
    setInvoiceNumber(''); // Will be regenerated by useEffect
  }

  const { subtotal, totalAmount } = useMemo(() => {
    const subtotal = purchaseItems.reduce((acc, item) => acc + item.total, 0);
    const totalAmount = subtotal - discount;
    return { subtotal, totalAmount };
  }, [purchaseItems, discount]);

  const filteredPurchasesHistory = useMemo(() => {
    return purchases.filter(purchase => 
        purchase.supplierName.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
        purchase.id.toLowerCase().includes(historySearchTerm.toLowerCase())
    );
  }, [purchases, historySearchTerm]);

  return (
    <>
      <PageHeader
        title="إدارة المشتريات"
        description="تسجيل فواتير الشراء وإدارة إدخالات الأدوية."
        action={<Button onClick={handleNewPurchase} variant="outline"><PlusCircle className="ml-2 h-4 w-4"/>فاتورة جديدة</Button>}
      />
      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>فاتورة المشتريات رقم: {invoiceNumber}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid sm:grid-cols-3 gap-6">
                 <div className="space-y-2 sm:col-span-3 relative">
                    <Label htmlFor="supplier-name">اسم المورد <span className="text-destructive">*</span></Label>
                    <div ref={supplierNameInputRef} className="flex gap-2">
                      <Input 
                          id="supplier-name" 
                          value={supplierName} 
                          onChange={handleSupplierNameChange} 
                          onFocus={() => supplierName && setSupplierSuggestions(suppliers.filter(s => s.name.toLowerCase().includes(supplierName.toLowerCase())))}
                          placeholder="ابحث عن مورد..." 
                          autoComplete="off"
                          className="flex-grow"
                      />
                    </div>
                    {showSupplierSuggestions && supplierSuggestions.length > 0 && (
                        <div ref={supplierSuggestionsRef} className="absolute z-20 w-full bg-card border rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
                            <ul>
                            {supplierSuggestions.map(s => (
                                <li key={s.id} onMouseDown={() => handleSupplierSuggestionClick(s)} className="p-2 hover:bg-accent cursor-pointer">
                                    {s.name}
                                </li>
                            ))}
                            </ul>
                        </div>
                    )}
                 </div>
              </div>

              <form className="grid gap-2 sm:grid-cols-2 lg:grid-cols-12 items-end border-t pt-6 mt-6" onSubmit={(e) => { e.preventDefault(); handleAddItem(); }}>
                   <div className="space-y-2 lg:col-span-4 relative">
                      <Label htmlFor="item-name">اسم المادة</Label>
                      <div ref={itemNameInputRef}>
                        <Input 
                            id="item-name" 
                            ref={itemNameInputRef}
                            value={itemName} 
                            onChange={handleItemNameChange} 
                            onFocus={() => itemName && setProductSuggestions(inventory.filter(p => p.name.toLowerCase().includes(itemName.toLowerCase())))}
                            placeholder="ابحث أو أدخل اسم مادة جديدة" 
                            autoComplete="off"
                        />
                      </div>
                      {showProductSuggestions && productSuggestions.length > 0 && (
                        <div ref={productSuggestionsRef} className="absolute z-10 w-full bg-card border rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
                           <ul>
                            {productSuggestions.map(p => (
                               <li key={p.id} onMouseDown={() => handleProductSuggestionClick(p)} className="p-2 hover:bg-accent cursor-pointer">
                                  {p.name}
                               </li>
                            ))}
                          </ul>
                        </div>
                      )}
                   </div>
                   <div className="space-y-2 lg:col-span-1">
                      <Label htmlFor="quantity">الكمية</Label>
                      <Input id="quantity" type="number" value={quantity} onChange={e => setQuantity(Number(e.target.value))} min="1" />
                   </div>
                   <div className="space-y-2 lg:col-span-1">
                      <Label htmlFor="bonus">البونص</Label>
                      <Input id="bonus" type="number" value={bonus} onChange={e => setBonus(Number(e.target.value))} min="0" />
                   </div>
                   <div className="space-y-2 lg:col-span-2">
                      <Label htmlFor="price">سعر الشراء</Label>
                      <Input id="price" type="number" value={purchasePrice} onChange={e => setPurchasePrice(Number(e.target.value))} min="0" />
                   </div>
                   <div className="space-y-2 lg:col-span-2">
                      <Label htmlFor="selling-price">سعر البيع</Label>
                      <Input id="selling-price" type="number" value={sellingPrice} onChange={e => setSellingPrice(Number(e.target.value))} min="0" />
                   </div>
                   <div className="space-y-2 lg:col-span-2">
                      <Label htmlFor="exp-date">تاريخ الانتهاء</Label>
                      <Input id="exp-date" type="date" value={expDate} onChange={e => setExpDate(e.target.value)} />
                   </div>
                   <div className="space-y-2 lg:col-span-2">
                      <Label htmlFor="batch-number">رقم الوجبة</Label>
                      <Input id="batch-number" value={batchNumber} onChange={e => setBatchNumber(e.target.value)} placeholder="Batch No." />
                   </div>
                   <div className="lg:col-span-2">
                      <Button type="submit" className="w-full">إضافة</Button>
                   </div>
              </form>

              <div className="mt-6 overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المادة</TableHead>
                      <TableHead>الكمية</TableHead>
                      <TableHead>بونص</TableHead>
                      <TableHead>سعر الشراء</TableHead>
                      <TableHead>ت. الانتهاء</TableHead>
                      <TableHead>ر. الوجبة</TableHead>
                      <TableHead>الإجمالي</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseItems.length === 0 && (
                        <TableRow>
                            <TableCell colSpan={8} className="text-center h-24">
                                لم تتم إضافة أي مواد بعد
                            </TableCell>
                        </TableRow>
                    )}
                    {purchaseItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.bonus}</TableCell>
                        <TableCell>{item.price.toLocaleString()} د.ع</TableCell>
                        <TableCell>{item.expDate}</TableCell>
                        <TableCell>{item.batchNumber}</TableCell>
                        <TableCell>{item.total.toLocaleString()} د.ع</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" onClick={() => handleRemoveItem(item.id)}>
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="md:col-span-1">
          <Card className="sticky top-20">
            <CardHeader>
              <CardTitle>ملخص الفاتورة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>المجموع الفرعي</span>
                <span>{subtotal.toLocaleString()} د.ع</span>
              </div>
              <div className="space-y-2">
                  <Label htmlFor="discount">الخصم</Label>
                  <Input id="discount" type="number" value={discount} onChange={e => setDiscount(Number(e.target.value) || 0)} min="0" placeholder="أدخل مبلغ الخصم"/>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-4">
                <span>المبلغ الإجمالي</span>
                <span>{totalAmount.toLocaleString()} د.ع</span>
              </div>
              <div className="space-y-2 pt-4">
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Textarea 
                        id="notes" 
                        value={notes} 
                        onChange={(e) => setNotes(e.target.value)} 
                        placeholder="أضف ملاحظات على الفاتورة..."
                        rows={4}
                    />
                </div>
            </CardContent>
            <CardFooter className="flex-col gap-2">
                 <Button size="lg" className="w-full" onClick={handleSavePurchase}>
                    <Save className="ml-2 h-4 w-4" />
                    حفظ وطباعة
                 </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
      
      {purchases.length > 0 && (
         <Card className="mt-8">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <History className="h-5 w-5 text-primary" />
                        <CardTitle>سجل فواتير المشتريات</CardTitle>
                    </div>
                    <div className="w-full max-w-sm">
                        <Input 
                            placeholder="بحث بالرقم أو اسم المورد..." 
                            value={historySearchTerm}
                            onChange={(e) => setHistorySearchTerm(e.target.value)}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>رقم الفاتورة</TableHead>
                                <TableHead>المورد</TableHead>
                                <TableHead>التاريخ</TableHead>
                                <TableHead className="text-right">الإجمالي</TableHead>
                                <TableHead className="text-right">الإجراءات</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredPurchasesHistory.slice().reverse().map(record => (
                                 <TableRow key={record.id}>
                                    <TableCell className="font-medium">{record.id}</TableCell>
                                    <TableCell>{record.supplierName}</TableCell>
                                    <TableCell>{record.date}</TableCell>
                                    <TableCell className="text-right">{record.totalAmount.toLocaleString()} د.ع</TableCell>
                                     <TableCell className="text-right">
                                       <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="icon">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف الفاتورة بشكل دائم وعكس تأثيرها على المخزون والحسابات.
                                                </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDelete(record.id)}>متابعة</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                 </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
      )}

    </>
  );
}
