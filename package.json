{"name": "nextn", "version": "0.1.0", "private": true, "main": "main.js", "scripts": {"dev": "next dev -p 9002", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "electron:dev": "concurrently \"npm:dev\" \"wait-on http://localhost:9002 && electron .\"", "electron:build": "next build && next export -o out && electron-builder"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "immer": "^10.1.1", "lucide-react": "^0.475.0", "next": "^14.2.8", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "concurrently": "^8.2.2", "electron": "^31.2.1", "electron-builder": "^24.13.3", "electron-is-dev": "^3.0.1", "firebase-tools": "^13.11.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "wait-on": "^7.2.0"}}