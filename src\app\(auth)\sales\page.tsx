
"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PlusCircle, Save, Trash2, History, Sparkles, Loader2, Printer } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { createRoot } from "react-dom/client";
import { InvoiceTemplate, type InvoiceTemplateProps } from "@/components/invoice/invoice-template";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { useStore } from "@/store/erp-store";
import type { Customer, SaleItem, SaleRecord, Product } from "@/store/erp-store";


export default function SalesPage() {
  const { 
    customers, 
    inventory, 
    sales,
    settings,
    addSale,
    deleteSale,
    lastSaleInvoiceNumber
  } = useStore();
  
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [customerName, setCustomerName] = useState("");
  const [customerId, setCustomerId] = useState<number | null>(null);
  const [paymentMethod, setPaymentMethod] = useState("later");
  const [discount, setDiscount] = useState(0);
  const [notes, setNotes] = useState(settings.invoiceNotes);

  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [bonus, setBonus] = useState(0);

  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [issueDate, setIssueDate] = useState('');
  const [historySearchTerm, setHistorySearchTerm] = useState("");
  const { toast } = useToast();

  const [customerSuggestions, setCustomerSuggestions] = useState<Customer[]>([]);
  const [showCustomerSuggestions, setShowCustomerSuggestions] = useState(false);
  const customerNameInputRef = useRef<HTMLInputElement>(null);
  const customerSuggestionsRef = useRef<HTMLDivElement>(null);
  const quantityInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const generateInvoiceDetails = () => {
        setInvoiceNumber((lastSaleInvoiceNumber + 1).toString());
        setIssueDate(new Date().toISOString().split('T')[0]);
    };

    if (!invoiceNumber) {
        generateInvoiceDetails();
    }
  }, [invoiceNumber, lastSaleInvoiceNumber]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        customerNameInputRef.current &&
        !customerNameInputRef.current.contains(event.target as Node) &&
        customerSuggestionsRef.current &&
        !customerSuggestionsRef.current.contains(event.target as Node)
      ) {
        setShowCustomerSuggestions(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleCustomerNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomerName(value);
    setCustomerId(null); // Reset customer id if name is manually changed
    if (value) {
      const filteredSuggestions = customers.filter(c => c.name.toLowerCase().includes(value.toLowerCase()));
      setCustomerSuggestions(filteredSuggestions);
      setShowCustomerSuggestions(true);
    } else {
      setCustomerSuggestions([]);
      setShowCustomerSuggestions(false);
    }
  };

  const handleCustomerSuggestionClick = (customer: Customer) => {
    setCustomerName(customer.name);
    setCustomerId(customer.id);
    setCustomerSuggestions([]);
    setShowCustomerSuggestions(false);
  };
  
  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
    quantityInputRef.current?.focus();
    quantityInputRef.current?.select();
  }

  const handleAddItem = () => {
    const product = inventory.find(p => `${p.id}-${p.batchNumber}` === selectedProduct);
    if (!product || quantity <= 0) return;

    if ((quantity + bonus) > product.stock) {
        toast({
            variant: "destructive",
            title: "كمية غير متوفرة",
            description: `إجمالي الكمية المطلوبة (${quantity + bonus}) من "${product.name}" غير متوفر. المتوفر: ${product.stock}`,
        });
        return;
    }

    const newItem: SaleItem = {
      id: product.id,
      name: product.name,
      quantity: quantity,
      price: product.sellingPrice,
      bonus: bonus,
      total: quantity * product.sellingPrice, // Total is calculated on paid quantity only
      batchNumber: product.batchNumber,
      expDate: product.expDate,
      purchasePrice: product.purchasePrice,
    };
    
    setSaleItems(prevItems => [...prevItems, newItem]);
    
    setSelectedProduct("");
    setQuantity(1);
    setBonus(0);
  };
  
  const handleRemoveItem = (id: number, batchNumber: string) => {
    setSaleItems(prevItems => prevItems.filter(item => !(item.id === id && item.batchNumber === batchNumber)));
  };

  const handleDelete = (invoiceId: string) => {
    deleteSale(invoiceId);
    toast({ title: 'تم الحذف', description: `تم حذف الفاتورة رقم ${invoiceId} بنجاح.` });
  };

    const handlePrint = (data: InvoiceTemplateProps) => {
        const printWindow = window.open('', '_blank', 'left=50,top=50,width=1000,height=800');
        if (!printWindow) return;

        const printDocument = printWindow.document;
        printDocument.write('<html><head><title>طباعة فاتورة</title>');
        
        const styleSheets = Array.from(document.styleSheets);
        styleSheets.forEach(ss => {
            if (ss.href) {
                const link = printDocument.createElement('link');
                link.rel = 'stylesheet';
                link.href = ss.href;
                printDocument.head.appendChild(link);
            } else if (ss.ownerNode instanceof HTMLStyleElement) {
                 const style = printDocument.createElement('style');
                 style.textContent = ss.ownerNode.innerHTML;
                 printDocument.head.appendChild(style);
            }
        });

        const waitForStyles = new Promise<void>((resolve) => {
            let loadedCount = 0;
            const links = printDocument.head.getElementsByTagName('link');
            if (links.length === 0) { resolve(); return; }
            for (let i = 0; i < links.length; i++) {
                links[i].onload = () => { if (++loadedCount === links.length) resolve(); };
                links[i].onerror = () => { if (++loadedCount === links.length) resolve(); };
            }
        });

        waitForStyles.then(() => {
            printDocument.write('<style>@media print { @page { size: A4; margin: 0; } body { -webkit-print-color-adjust: exact; print-color-adjust: exact; margin: 1.6cm; } } </style>');
            printDocument.write('</head><body dir="rtl"></body></html>');
            printDocument.close(); 
            
            const printContentEl = printDocument.createElement('div');
            printDocument.body.appendChild(printContentEl);
            
            const root = createRoot(printContentEl);
            root.render(<InvoiceTemplate {...data} />);
            
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }, 500); 
        });
    }

  const handleSaveInvoice = () => {
      if(!customerName || !customerId) {
          toast({
              variant: "destructive",
              title: "حقل مطلوب",
              description: "الرجاء اختيار عميل من القائمة."
          });
          return;
      }
      if (saleItems.length === 0) {
        toast({
            variant: "destructive",
            title: "الفاتورة فارغة",
            description: "الرجاء إضافة صنف واحد على الأقل."
        });
        return;
      }

      const subtotal = saleItems.reduce((acc, item) => acc + (item.quantity * item.price), 0);
      const totalAmount = subtotal - discount;

      const newSaleRecord: Omit<SaleRecord, 'status' | 'paidAmount'> = {
          id: invoiceNumber,
          customerId: customerId,
          customerName: customerName,
          date: issueDate,
          items: saleItems,
          discount,
          notes,
          paymentMethod: paymentMethod as 'cash' | 'card' | 'later',
          totalAmount,
      };
      
      addSale(newSaleRecord);

      toast({
          title: "تم الحفظ بنجاح!",
          description: `تم حفظ الفاتورة وتحديث المخزون والحسابات والصندوق.`
      });

      // Prepare data for printing before resetting state
      const printData: InvoiceTemplateProps = {
        invoiceNumber: newSaleRecord.id,
        issueDate: newSaleRecord.date,
        invoiceType: 'فاتورة بيع',
        paymentMethod: paymentMethod === 'cash' ? 'نقداً' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'آجل',
        region: 'بغداد',
        customerName: newSaleRecord.customerName,
        items: newSaleRecord.items.map((item, index) => ({
          seq: index + 1,
          name: item.name,
          quantity: item.quantity,
          bonus: item.bonus,
          price: item.price,
          exp: item.expDate,
          notes: ''
        })),
        discount: newSaleRecord.discount,
        notes: newSaleRecord.notes,
        companyInfo: settings.companyInfo,
        printLogoSvg: settings.printLogoSvg,
      };

      handlePrint(printData);
      handleNewInvoice();
  }

  const handleNewInvoice = () => {
    setSaleItems([]);
    setCustomerName("");
    setCustomerId(null);
    setPaymentMethod("later");
    setDiscount(0);
    setNotes(settings.invoiceNotes);
    setShowCustomerSuggestions(false);
    setInvoiceNumber(''); // Will be regenerated by useEffect
  }

  const { subtotal, totalBonusValue, totalAmount } = useMemo(() => {
    const subtotal = saleItems.reduce((acc, item) => acc + (item.quantity * item.price), 0);
    const totalBonusValue = saleItems.reduce((acc, item) => acc + (item.bonus * item.price), 0);
    const totalAmount = subtotal - discount;
    return { subtotal, totalBonusValue, totalAmount };
  }, [saleItems, discount]);

  const uniqueInventoryItems = useMemo(() => {
    const unique = new Map<string, Product>();
    inventory.forEach(item => {
        const key = `${item.name} (${item.batchNumber}) - ${item.expDate}`;
         if (!unique.has(key)) {
            unique.set(key, item);
        }
    });
    return Array.from(unique.values());
  }, [inventory])

  const filteredSalesHistory = useMemo(() => {
      return sales.filter(sale => 
          sale.customerName.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
          sale.id.toLowerCase().includes(historySearchTerm.toLowerCase())
      );
  }, [sales, historySearchTerm]);

  return (
    <>
      <PageHeader
        title="إدارة المبيعات"
        description="تسجيل معاملات البيع وإدارة الفواتير."
        action={<Button onClick={handleNewInvoice} variant="outline" className="no-print"><PlusCircle className="ml-2 h-4 w-4"/>فاتورة جديدة</Button>}
        className="no-print"
      />
      <div className="grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="no-print">
            <CardHeader>
              <CardTitle>فاتورة مبيعات رقم: {invoiceNumber}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="space-y-2 relative">
                  <Label htmlFor="customer-name">اسم العميل <span className="text-destructive">*</span></Label>
                  <div ref={customerNameInputRef} className="flex gap-2">
                    <Input 
                      id="customer-name"
                      className="flex-grow" 
                      value={customerName} 
                      onChange={handleCustomerNameChange}
                      onFocus={() => customerName && setCustomerSuggestions(customers.filter(c => c.name.toLowerCase().includes(customerName.toLowerCase())))}
                      placeholder="ابحث عن عميل..." 
                      autoComplete="off"
                    />
                  </div>
                   {showCustomerSuggestions && customerSuggestions.length > 0 && (
                        <div ref={customerSuggestionsRef} className="absolute z-10 w-full bg-card border rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
                           <ul>
                            {customerSuggestions.map(c => (
                               <li key={c.id} onMouseDown={() => handleCustomerSuggestionClick(c)} className="p-2 hover:bg-accent cursor-pointer">
                                  {c.name}
                               </li>
                            ))}
                          </ul>
                        </div>
                   )}
                </div>
              </div>
              <div className="mt-6">
                <form className="grid gap-4 sm:grid-cols-6 items-end" onSubmit={(e) => { e.preventDefault(); handleAddItem(); }}>
                   <div className="space-y-2 sm:col-span-6 md:col-span-3">
                      <Label htmlFor="product">اختر منتج</Label>
                      <Select value={selectedProduct} onValueChange={handleProductSelect}>
                        <SelectTrigger id="product">
                          <SelectValue placeholder="بحث عن منتج..." />
                        </SelectTrigger>
                        <SelectContent>
                          {uniqueInventoryItems.map(p => <SelectItem key={`${p.id}-${p.batchNumber}`} value={`${p.id}-${p.batchNumber}`} disabled={p.stock <= 0}>{p.name} ({p.batchNumber}) (متوفر: {p.stock})</SelectItem>)}
                        </SelectContent>
                      </Select>
                   </div>
                   <div className="space-y-2 sm:col-span-2 md:col-span-1">
                      <Label htmlFor="quantity">الكمية</Label>
                      <Input ref={quantityInputRef} id="quantity" type="number" value={quantity} onChange={e => setQuantity(Number(e.target.value))} min="1" />
                   </div>
                   <div className="space-y-2 sm:col-span-2 md:col-span-1">
                      <Label htmlFor="bonus">البونص</Label>
                      <Input id="bonus" type="number" value={bonus} onChange={e => setBonus(Number(e.target.value))} min="0" />
                   </div>
                   <div className="sm:col-span-2 md:col-span-1">
                      <Button type="submit" className="w-full">إضافة</Button>
                   </div>
                </form>
              </div>

              <div className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المنتج</TableHead>
                      <TableHead>الكمية</TableHead>
                      <TableHead>البونص</TableHead>
                      <TableHead>السعر</TableHead>
                      <TableHead>الإجمالي</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {saleItems.length === 0 && (
                        <TableRow>
                            <TableCell colSpan={6} className="text-center h-24">
                                لم تتم إضافة أي أصناف بعد
                            </TableCell>
                        </TableRow>
                    )}
                    {saleItems.map((item) => (
                      <TableRow key={`${item.id}-${item.batchNumber}`}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.bonus}</TableCell>
                        <TableCell>{item.price.toLocaleString()} د.ع</TableCell>
                        <TableCell>{item.total.toLocaleString()} د.ع</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" onClick={() => handleRemoveItem(item.id, item.batchNumber)}>
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="lg:col-span-1">
          <Card className="sticky top-20 no-print">
            <CardHeader>
              <CardTitle>ملخص الفاتورة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>المجموع الفرعي</span>
                <span>{subtotal.toLocaleString()} د.ع</span>
              </div>
              <div className="space-y-2">
                  <Label htmlFor="discount">الخصم</Label>
                  <Input id="discount" type="number" value={discount} onChange={e => setDiscount(Number(e.target.value) || 0)} min="0" placeholder="أدخل مبلغ الخصم"/>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-4">
                <span>المبلغ الإجمالي</span>
                <span>{totalAmount.toLocaleString()} د.ع</span>
              </div>
               <div className="space-y-2 pt-4">
                  <Label htmlFor="payment-method">طريقة الدفع</Label>
                  <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                    <SelectTrigger id="payment-method">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cash">نقداً</SelectItem>
                      <SelectItem value="card">بطاقة ائتمان</SelectItem>
                       <SelectItem value="later">آجل</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                 <div className="space-y-2 pt-4">
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Textarea 
                        id="notes" 
                        value={notes} 
                        onChange={(e) => setNotes(e.target.value)} 
                        placeholder="أضف ملاحظات على الفاتورة..."
                        rows={3}
                    />
                </div>
            </CardContent>
            <CardFooter className="flex-col gap-2">
                 <Button size="lg" className="w-full" onClick={handleSaveInvoice}>
                    <Save className="ml-2 h-4 w-4" />
                    حفظ وطباعة
                 </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
      
       {sales.length > 0 && (
         <Card className="mt-8 no-print">
            <CardHeader>
                 <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <History className="h-5 w-5 text-primary" />
                        <CardTitle>سجل فواتير المبيعات</CardTitle>
                    </div>
                    <div className="w-full max-w-sm">
                        <Input 
                            placeholder="بحث بالرقم أو اسم العميل..." 
                            value={historySearchTerm}
                            onChange={(e) => setHistorySearchTerm(e.target.value)}
                        />
                    </div>
                 </div>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>رقم الفاتورة</TableHead>
                                <TableHead>العميل</TableHead>
                                <TableHead>التاريخ</TableHead>
                                <TableHead className="text-right">الإجمالي</TableHead>
                                <TableHead className="text-right">الإجراءات</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredSalesHistory.slice().reverse().map(record => (
                                 <TableRow key={record.id}>
                                    <TableCell className="font-medium">{record.id}</TableCell>
                                    <TableCell>{record.customerName}</TableCell>
                                    <TableCell>{record.date}</TableCell>
                                    <TableCell className="text-right">{record.totalAmount.toLocaleString()} د.ع</TableCell>
                                    <TableCell className="text-right">
                                       <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="icon">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف الفاتورة بشكل دائم وعكس تأثيرها على المخزون والحسابات.
                                                </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDelete(record.id)}>متابعة</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                 </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
      )}
    </>
  );
}
