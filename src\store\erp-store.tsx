
'use client';

import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { produce } from 'immer';
import { Logo } from '@/components/icons';

// --- Type Definitions ---
export type ExpenseCategory = 'salaries' | 'rent' | 'support' | 'cash_difference' | 'damaged_goods' | 'other';

export interface Product {
  id: number;
  name: string;
  batchNumber: string;
  stock: number;
  purchasePrice: number;
  sellingPrice: number;
  expDate: string;
}

export interface Customer {
  id: number;
  name: string;
  phone: string;
  address: string;
  balance: number;
}

export interface Supplier {
  id: number;
  name: string;
  phone: string;
  address: string;
  balance: number; // Positive means we owe them, negative means they owe us (credit)
}

export interface CashboxTransaction {
    id: number;
    date: string;
    description: string;
    type: 'income' | 'expense';
    amount: number;
    category?: ExpenseCategory; // Optional: for expense categorization
    partyName?: string;
}

export interface SaleItem {
    id: number; // Product ID
    name: string;
    quantity: number;
    price: number;
    purchasePrice: number;
    bonus: number;
    total: number;
    batchNumber: string;
    expDate: string;
}

export interface SaleRecord {
    id: string; // Invoice number
    customerId: number;
    customerName: string;
    date: string; // YYYY-MM-DD
    items: SaleItem[];
    discount: number;
    notes: string;
    paymentMethod: 'cash' | 'card' | 'later';
    totalAmount: number;
    paidAmount: number;
    status: 'paid' | 'unpaid' | 'partial' | 'cancelled';
}

export interface PurchaseItem {
  id: number; // temp id
  name: string;
  quantity: number;
  bonus: number;
  price: number;
  sellingPrice: number;
  expDate: string; // YYYY-MM-DD
  batchNumber: string;
  total: number;
}

export interface PurchaseRecord {
    id: string; // Invoice number
    supplierId: number;
    supplierName: string;
    date: string; // YYYY-MM-DD
    items: PurchaseItem[];
    discount: number;
    notes: string;
    totalAmount: number;
    paidAmount: number;
    status: 'paid' | 'unpaid' | 'partial' | 'cancelled';
}

export interface ReturnItem {
    id: number; // product id
    name: string;
    originalQuantity: number;
    originalBonus: number;
    returnQuantity: number;
    returnBonus: number;
    price: number;
    expDate: string; // YYYY-MM-DD
    batchNumber: string;
}

export interface ReturnRecord {
    id: string;
    type: 'sales' | 'purchase';
    originalInvoiceId: string;
    customerOrSupplier: string;
    customerOrSupplierId: number;
    date: string; // YYYY-MM-DD
    amount: number; // Financial impact amount (only from paid items)
    items: ReturnItem[];
    notes?: string;
    refundMethod: 'cash' | 'credit';
}

export interface Payment {
    partyType: 'customer' | 'supplier';
    partyId: number;
    partyName: string;
    amount: number;
}

export interface Settings {
    companyInfo: {
        name: string;
        description: string;
        address: string;
    };
    invoiceNotes: string;
    systemLogoSvg: string | null;
    printLogoSvg: string | null;
}

export interface ActivityLog {
    id: number;
    timestamp: string;
    user: string; // In a real app, this would be a user ID
    action: string;
    details: string;
}

export type Permission = 'read' | 'create' | 'update' | 'delete';
export type Module = 'dashboard' | 'sales' | 'purchases' | 'returns' | 'customers' | 'suppliers' | 'inventory' | 'cashbox' | 'reports' | 'settings' | 'activityLog';
export type RolePermissions = Record<Module, Record<Permission, boolean>>;


// --- State and Reducer ---

export interface ErpState {
  inventory: Product[];
  customers: Customer[];
  suppliers: Supplier[];
  cashboxTransactions: CashboxTransaction[];
  sales: SaleRecord[];
  purchases: PurchaseRecord[];
  returns: ReturnRecord[];
  settings: Settings;
  activityLog: ActivityLog[];
  permissions: Record<string, RolePermissions>;
  lastSaleInvoiceNumber: number;
  lastPurchaseInvoiceNumber: number;
}

type ErpAction =
  | { type: 'ADD_SALE'; payload: Omit<SaleRecord, 'status' | 'paidAmount'> }
  | { type: 'DELETE_SALE'; payload: string }
  | { type: 'ADD_PURCHASE'; payload: Omit<PurchaseRecord, 'status' | 'paidAmount'> }
  | { type: 'DELETE_PURCHASE'; payload: string }
  | { type: 'ADD_RETURN'; payload: ReturnRecord }
  | { type: 'DELETE_RETURN'; payload: string }
  | { type: 'ADD_CUSTOMER'; payload: Customer }
  | { type: 'DELETE_CUSTOMER'; payload: number }
  | { type: 'ADD_SUPPLIER'; payload: Supplier }
  | { type: 'DELETE_SUPPLIER'; payload: number }
  | { type: 'ADD_CASHBOX_TRANSACTION'; payload: Omit<CashboxTransaction, 'id'> & { id?: number } }
  | { type: 'UPDATE_CASHBOX_TRANSACTION'; payload: { id: number; data: Partial<CashboxTransaction> } }
  | { type: 'DELETE_CASHBOX_TRANSACTION'; payload: number }
  | { type: 'UPDATE_INVENTORY_STOCK'; payload: { productId: number; batchNumber: string; change: number } }
  | { type: 'ADD_OR_UPDATE_INVENTORY_ITEM', payload: Omit<Product, 'id'> & { id?: number } }
  | { type: 'UPDATE_INVENTORY_ITEM'; payload: { id: number; batchNumber: string; data: Product } }
  | { type: 'DELETE_INVENTORY_ITEM'; payload: { id: number; batchNumber: string } }
  | { type: 'ADD_PAYMENT'; payload: Payment }
  | { type: 'SETTLE_INVOICE'; payload: { invoiceId: string; type: 'sale' | 'purchase', amount: number } }
  | { type: 'UPDATE_SETTINGS', payload: Partial<Settings> }
  | { type: 'SET_SYSTEM_LOGO', payload: string }
  | { type: 'SET_PRINT_LOGO', payload: string }
  | { type: 'HYDRATE_STATE'; payload: ErpState };

const modulesAr: Record<Module, string> = {
    dashboard: "لوحة التحكم",
    sales: "المبيعات",
    purchases: "المشتريات",
    returns: "المرتجعات",
    customers: "العملاء",
    suppliers: "الموردون",
    inventory: "المخزون",
    cashbox: "صندوق النقد",
    reports: "التقارير",
    settings: "الإعدادات",
    activityLog: "سجل الأنشطة",
};

const allPermissions: Record<Permission, boolean> = { read: true, create: true, update: true, delete: true };


const initialPermissions: Record<string, RolePermissions> = {
    'مدير': Object.fromEntries(Object.keys(modulesAr).map(m => [m, {...allPermissions}])) as RolePermissions,
    'محاسب': {
        dashboard: { read: true, create: false, update: false, delete: false },
        sales: { read: true, create: true, update: true, delete: false },
        purchases: { read: true, create: true, update: true, delete: false },
        returns: { read: true, create: true, update: true, delete: false },
        customers: { read: true, create: true, update: true, delete: false },
        suppliers: { read: true, create: true, update: true, delete: false },
        inventory: { read: true, create: false, update: false, delete: false },
        cashbox: { read: true, create: true, update: true, delete: false },
        reports: { read: true, create: false, update: false, delete: false },
        settings: { read: false, create: false, update: false, delete: false },
        activityLog: { read: true, create: false, update: false, delete: false },
    },
    'بائع': {
        dashboard: { read: true, create: false, update: false, delete: false },
        sales: { read: true, create: true, update: false, delete: false },
        purchases: { read: false, create: false, update: false, delete: false },
        returns: { read: true, create: true, update: false, delete: false },
        customers: { read: true, create: true, update: true, delete: false },
        suppliers: { read: false, create: false, update: false, delete: false },
        inventory: { read: true, create: false, update: false, delete: false },
        cashbox: { read: false, create: false, update: false, delete: false },
        reports: { read: false, create: false, update: false, delete: false },
        settings: { read: false, create: false, update: false, delete: false },
        activityLog: { read: false, create: false, update: false, delete: false },
    },
};

const initialState: ErpState = {
  inventory: [],
  customers: [],
  suppliers: [],
  cashboxTransactions: [],
  sales: [],
  purchases: [],
  returns: [],
  settings: {
    companyInfo: {
        name: "شركة العالم",
        description: "لتجارة و دعاية مواد التجميل ومنتجات العناية بالبشرة والشعر",
        address: "العراق - بغداد - شارع المتنبي - بناية رقم 50\n07801234567 - 07701234567",
    },
    invoiceNotes: "الرجاء التحقق من البضاعة قبل الاستلام. البضاعة المباعة لا ترد ولا تستبدل بعد 7 أيام.",
    systemLogoSvg: null,
    printLogoSvg: null,
  },
  activityLog: [],
  permissions: initialPermissions,
  lastSaleInvoiceNumber: 99,
  lastPurchaseInvoiceNumber: 99,
};

const erpReducer = (state: ErpState, action: ErpAction): ErpState => {
  return produce(state, draft => {
    
    // --- Helper functions ---
    const logActivity = (action: string, details: string) => {
        draft.activityLog.unshift({
            id: Date.now(),
            timestamp: new Date().toISOString(),
            user: "admin", // Mock user
            action,
            details
        });
    }

    const recalculateCustomerBalance = (customerId: number) => {
        const customer = draft.customers.find(c => c.id === customerId);
        if (customer) {
            const customerSales = draft.sales.filter(s => s.customerId === customerId);
            const totalDebt = customerSales.reduce((acc, s) => acc + (s.totalAmount - s.paidAmount), 0);
            customer.balance = totalDebt;
        }
    };
    
    const recalculateSupplierBalance = (supplierId: number) => {
        const supplier = draft.suppliers.find(s => s.id === supplierId);
        if (supplier) {
            const supplierPurchases = draft.purchases.filter(p => p.supplierId === supplierId);
            const totalDebt = supplierPurchases.reduce((acc, p) => acc + (p.totalAmount - p.paidAmount), 0);
            supplier.balance = totalDebt;
        }
    };
    
    // --- Reducer Logic ---
    switch (action.type) {
      case 'HYDRATE_STATE':
        return { ...initialState, ...action.payload };
      case 'ADD_SALE': {
        draft.lastSaleInvoiceNumber += 1;
        const saleRecord = { ...action.payload, id: draft.lastSaleInvoiceNumber.toString() };
        
        const isCashOrCard = saleRecord.paymentMethod === 'cash' || saleRecord.paymentMethod === 'card';

        const newSale: SaleRecord = {
          ...saleRecord,
          paidAmount: isCashOrCard ? saleRecord.totalAmount : 0,
          status: isCashOrCard ? 'paid' : 'unpaid',
        };
        draft.sales.unshift(newSale);

        saleRecord.items.forEach(item => {
            const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
            if(inventoryItem) {
                inventoryItem.stock -= (item.quantity + item.bonus);
            }
        });

        if (isCashOrCard) {
            draft.cashboxTransactions.unshift({
                id: Date.now(),
                date: new Date().toISOString().split('T')[0],
                description: `مبيعات نقدية فاتورة ${newSale.id}`,
                type: 'income',
                amount: newSale.totalAmount,
            });
        }
        
        recalculateCustomerBalance(saleRecord.customerId);
        logActivity('إنشاء فاتورة مبيعات', `فاتورة رقم ${newSale.id} للعميل ${newSale.customerName} بقيمة ${newSale.totalAmount.toLocaleString()}`);
        break;
      }
      case 'DELETE_SALE': {
          const saleId = action.payload;
          const saleIndex = draft.sales.findIndex(s => s.id === saleId);
          if (saleIndex === -1) break;

          const saleToDelete = draft.sales[saleIndex];
          const customerId = saleToDelete.customerId;

          // Revert inventory stock
          saleToDelete.items.forEach(item => {
              const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
              if (inventoryItem) {
                  inventoryItem.stock += (item.quantity + item.bonus);
              }
          });
          
          // Delete related cashbox transactions
          draft.cashboxTransactions = draft.cashboxTransactions.filter(
              t => !t.description.includes(`فاتورة ${saleToDelete.id}`)
          );

          // Delete related returns and revert their effects
          const relatedReturns = draft.returns.filter(r => r.originalInvoiceId === saleId);
          relatedReturns.forEach(returnToDelete => {
              // Revert inventory for the return
              returnToDelete.items.forEach(item => {
                  const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                  if (inventoryItem) {
                      inventoryItem.stock -= (item.returnQuantity + item.returnBonus);
                  }
              });
              // Revert cashbox if it was a cash refund
              if (returnToDelete.refundMethod === 'cash') {
                  draft.cashboxTransactions = draft.cashboxTransactions.filter(t => !t.description.includes(returnToDelete.id));
              }
          });
          draft.returns = draft.returns.filter(r => r.originalInvoiceId !== saleId);
          
          // Finally, delete the sale
          draft.sales.splice(saleIndex, 1);
          recalculateCustomerBalance(customerId);
          logActivity('حذف فاتورة مبيعات', `فاتورة رقم ${saleId} وكل السجلات المرتبطة بها`);
          break;
      }
      case 'ADD_PURCHASE': {
        draft.lastPurchaseInvoiceNumber += 1;
        const purchaseRecord = { ...action.payload, id: draft.lastPurchaseInvoiceNumber.toString() };

        const purchaseWithStatus: PurchaseRecord = {
            ...purchaseRecord,
            paidAmount: 0,
            status: 'unpaid',
        };
        draft.purchases.unshift(purchaseWithStatus);

        purchaseRecord.items.forEach(item => {
            const itemIndex = draft.inventory.findIndex(
                (p) => p.name === item.name && p.batchNumber === item.batchNumber
            );
            if (itemIndex !== -1) {
                draft.inventory[itemIndex].stock += (item.quantity + item.bonus);
            } else {
                draft.inventory.unshift({ 
                    id: Date.now(), 
                    name: item.name,
                    batchNumber: item.batchNumber,
                    stock: item.quantity + item.bonus,
                    purchasePrice: item.price,
                    sellingPrice: item.sellingPrice,
                    expDate: item.expDate,
                });
            }
        });

        recalculateSupplierBalance(purchaseRecord.supplierId);
        logActivity('إنشاء فاتورة مشتريات', `فاتورة رقم ${purchaseWithStatus.id} من المورد ${purchaseWithStatus.supplierName}`);
        break;
      }
      case 'DELETE_PURCHASE': {
          const purchaseId = action.payload;
          const purchaseIndex = draft.purchases.findIndex(p => p.id === purchaseId);
          if (purchaseIndex === -1) break;

          const purchaseToDelete = draft.purchases[purchaseIndex];
          const supplierId = purchaseToDelete.supplierId;
          
          // Revert inventory stock
          purchaseToDelete.items.forEach(item => {
              const inventoryItem = draft.inventory.find(p => p.name === item.name && p.batchNumber === item.batchNumber);
              if (inventoryItem) {
                  inventoryItem.stock -= (item.quantity + item.bonus);
              }
          });

          // Delete related cashbox transactions
          draft.cashboxTransactions = draft.cashboxTransactions.filter(
              t => !t.description.includes(`فاتورة ${purchaseToDelete.id}`)
          );

           // Delete related returns and revert their effects
          const relatedReturns = draft.returns.filter(r => r.originalInvoiceId === purchaseId);
          relatedReturns.forEach(returnToDelete => {
              // Revert inventory for the return
              returnToDelete.items.forEach(item => {
                  const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                  if (inventoryItem) {
                      inventoryItem.stock += (item.returnQuantity + item.returnBonus);
                  }
              });
              // Revert cashbox if it was a cash refund
              if (returnToDelete.refundMethod === 'cash') {
                  draft.cashboxTransactions = draft.cashboxTransactions.filter(t => !t.description.includes(returnToDelete.id));
              }
          });
          draft.returns = draft.returns.filter(r => r.originalInvoiceId !== purchaseId);

          // Finally, delete the purchase
          draft.purchases.splice(purchaseIndex, 1);
          recalculateSupplierBalance(supplierId);
          logActivity('حذف فاتورة مشتريات', `فاتورة رقم ${purchaseId} وكل السجلات المرتبطة بها`);
          break;
      }
      case 'ADD_RETURN': {
        const returnRecord = action.payload;
        draft.returns.unshift(returnRecord);

        if (returnRecord.type === 'sales') {
            const originalSale = draft.sales.find(s => s.id === returnRecord.originalInvoiceId);
            if (originalSale) {
                // Adjust invoice totals based on the returned items' financial value
                originalSale.totalAmount -= returnRecord.amount;
                // If the return is for credit, the paid amount is effectively reduced relative to the new total
                if (returnRecord.refundMethod === 'credit') {
                   // This doesn't change paidAmount directly, but recalculates status based on new total
                } else { // 'cash'
                    originalSale.paidAmount -= returnRecord.amount;
                    draft.cashboxTransactions.unshift({
                        id: Date.now(),
                        date: new Date().toISOString().split('T')[0],
                        description: `استرجاع نقدي للمرتجع ${returnRecord.id}`,
                        type: 'expense',
                        amount: returnRecord.amount,
                    });
                }
                originalSale.status = originalSale.paidAmount >= originalSale.totalAmount ? 'paid' : (originalSale.paidAmount > 0 ? 'partial' : 'unpaid');
            }

            returnRecord.items.forEach(item => {
                const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                if (inventoryItem) {
                    inventoryItem.stock += (item.returnQuantity + item.returnBonus);
                }
            });
            recalculateCustomerBalance(returnRecord.customerOrSupplierId);

        } else { // purchase return
             const originalPurchase = draft.purchases.find(p => p.id === returnRecord.originalInvoiceId);
             if (originalPurchase) {
                originalPurchase.totalAmount -= returnRecord.amount;
                 if (returnRecord.refundMethod === 'cash') {
                    originalPurchase.paidAmount -= returnRecord.amount;
                     draft.cashboxTransactions.unshift({
                        id: Date.now(),
                        date: new Date().toISOString().split('T')[0],
                        description: `استرجاع نقدي من مرتجع المشتريات ${returnRecord.id}`,
                        type: 'income',
                        amount: returnRecord.amount,
                    });
                }
                originalPurchase.status = originalPurchase.paidAmount >= originalPurchase.totalAmount ? 'paid' : (originalPurchase.paidAmount > 0 ? 'partial' : 'unpaid');
             }

             returnRecord.items.forEach(item => {
                const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                if (inventoryItem) {
                    inventoryItem.stock -= (item.returnQuantity + item.returnBonus);
                }
            });
            recalculateSupplierBalance(returnRecord.customerOrSupplierId);
        }
        logActivity(`إنشاء مرتجع ${returnRecord.type === 'sales' ? 'مبيعات' : 'مشتريات'}`, `مرتجع رقم ${returnRecord.id} ضد الفاتورة ${returnRecord.originalInvoiceId}`);
        break;
      }
       case 'DELETE_RETURN': {
          const returnId = action.payload;
          const returnIndex = draft.returns.findIndex(r => r.id === returnId);
          if (returnIndex === -1) break;

          const returnToDelete = draft.returns[returnIndex];

          if (returnToDelete.type === 'sales') {
              returnToDelete.items.forEach(item => {
                  const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                  if (inventoryItem) {
                      inventoryItem.stock -= (item.returnQuantity + item.returnBonus);
                  }
              });

              const originalSale = draft.sales.find(s => s.id === returnToDelete.originalInvoiceId);
              if (originalSale) {
                  originalSale.totalAmount += returnToDelete.amount;
                  if (returnToDelete.refundMethod === 'cash') {
                      originalSale.paidAmount += returnToDelete.amount;
                  }
                  originalSale.status = originalSale.paidAmount >= originalSale.totalAmount ? 'paid' : (originalSale.paidAmount > 0 ? 'partial' : 'unpaid');
              }
              
              if(returnToDelete.refundMethod === 'cash') {
                   const cashboxTxIndex = draft.cashboxTransactions.findIndex(t => t.description.includes(returnToDelete.id));
                    if(cashboxTxIndex !== -1) {
                        draft.cashboxTransactions.splice(cashboxTxIndex, 1);
                    }
              }

              draft.returns.splice(returnIndex, 1);
              recalculateCustomerBalance(returnToDelete.customerOrSupplierId);

          } else { // purchase return
              returnToDelete.items.forEach(item => {
                  const inventoryItem = draft.inventory.find(p => p.id === item.id && p.batchNumber === item.batchNumber);
                  if (inventoryItem) {
                      inventoryItem.stock += (item.returnQuantity + item.returnBonus);
                  }
              });
              
              const originalPurchase = draft.purchases.find(p => p.id === returnToDelete.originalInvoiceId);
              if (originalPurchase) {
                  originalPurchase.totalAmount += returnToDelete.amount;
                  if (returnToDelete.refundMethod === 'cash') {
                      originalPurchase.paidAmount += returnToDelete.amount;
                  }
                  originalPurchase.status = originalPurchase.paidAmount >= originalPurchase.totalAmount ? 'paid' : (originalPurchase.paidAmount > 0 ? 'partial' : 'unpaid');
              }

              if(returnToDelete.refundMethod === 'cash') {
                   const cashboxTxIndex = draft.cashboxTransactions.findIndex(t => t.description.includes(returnToDelete.id));
                    if(cashboxTxIndex !== -1) {
                        draft.cashboxTransactions.splice(cashboxTxIndex, 1);
                    }
              }

              draft.returns.splice(returnIndex, 1);
              recalculateSupplierBalance(returnToDelete.customerOrSupplierId);
          }
           logActivity(`حذف مرتجع ${returnToDelete.type === 'sales' ? 'مبيعات' : 'مشتريات'}`, `مرتجع رقم ${returnId}`);
          break;
      }
      case 'ADD_CUSTOMER':
        draft.customers.unshift(action.payload);
        logActivity('إضافة عميل', `تمت إضافة العميل: ${action.payload.name}`);
        break;
      case 'DELETE_CUSTOMER':
        const customerToDelete = draft.customers.find(c => c.id === action.payload);
        if (customerToDelete) logActivity('حذف عميل', `تم حذف العميل: ${customerToDelete.name}`);
        draft.customers = draft.customers.filter(c => c.id !== action.payload);
        break;
      case 'ADD_SUPPLIER':
        draft.suppliers.unshift(action.payload);
        logActivity('إضافة مورد', `تمت إضافة المورد: ${action.payload.name}`);
        break;
      case 'DELETE_SUPPLIER':
        const supplierToDelete = draft.suppliers.find(s => s.id === action.payload);
        if(supplierToDelete) logActivity('حذف مورد', `تم حذف المورد: ${supplierToDelete.name}`);
        draft.suppliers = draft.suppliers.filter(s => s.id !== action.payload);
        break;
      case 'ADD_CASHBOX_TRANSACTION':
        const newTx = {id: Date.now(), ...action.payload};
        draft.cashboxTransactions.unshift(newTx);
        logActivity('إضافة معاملة صندوق', `${newTx.description} (${newTx.amount.toLocaleString()})`);
        break;
      case 'UPDATE_CASHBOX_TRANSACTION': {
        const txIndex = draft.cashboxTransactions.findIndex(t => t.id === action.payload.id);
        if (txIndex !== -1) {
          logActivity('تعديل معاملة صندوق', `تعديل المعاملة: ${draft.cashboxTransactions[txIndex].description}`);
          draft.cashboxTransactions[txIndex] = { ...draft.cashboxTransactions[txIndex], ...action.payload.data };
        }
        break;
      }
       case 'DELETE_CASHBOX_TRANSACTION': {
        const txId = action.payload;
        const txIndex = draft.cashboxTransactions.findIndex(t => t.id === txId);
        if (txIndex === -1) break;

        const txToDelete = draft.cashboxTransactions[txIndex];
        const description = txToDelete.description;
        const amount = txToDelete.amount;

        logActivity('حذف معاملة صندوق', `حذف المعاملة: ${txToDelete.description}`);

        const invoiceIdRegex = /(INV|PUR)-[\w\-]+/;
        const match = description.match(invoiceIdRegex);

        if (match) {
            const invoiceId = match[0];
            const sale = draft.sales.find(s => s.id === invoiceId);
            if (sale) {
                sale.paidAmount -= amount;
                sale.status = sale.paidAmount >= sale.totalAmount ? 'paid' : (sale.paidAmount > 0 ? 'partial' : 'unpaid');
                recalculateCustomerBalance(sale.customerId);
            }
            
            const purchase = draft.purchases.find(p => p.id === invoiceId);
            if (purchase) {
                purchase.paidAmount -= amount;
                purchase.status = purchase.paidAmount >= purchase.totalAmount ? 'paid' : (purchase.paidAmount > 0 ? 'partial' : 'unpaid');
                recalculateSupplierBalance(purchase.supplierId);
            }
        }
        
        draft.cashboxTransactions.splice(txIndex, 1);
        break;
    }
      case 'UPDATE_INVENTORY_STOCK': {
        const item = draft.inventory.find(p => p.id === action.payload.productId && p.batchNumber === action.payload.batchNumber);
        if (item) {
          item.stock += action.payload.change;
        }
        break;
      }
      case 'ADD_OR_UPDATE_INVENTORY_ITEM': {
        const itemIndex = draft.inventory.findIndex(
            (p) => p.name === action.payload.name && p.batchNumber === action.payload.batchNumber
        );
        if (itemIndex !== -1) {
            draft.inventory[itemIndex].stock += action.payload.stock;
            logActivity('تحديث كمية منتج', `${action.payload.name} (${action.payload.batchNumber})`);
        } else {
            draft.inventory.unshift({ id: Date.now(), ...action.payload });
            logActivity('إضافة منتج للمخزون', `${action.payload.name} (${action.payload.batchNumber})`);
        }
        break;
      }
      case 'UPDATE_INVENTORY_ITEM': {
        const itemIndex = draft.inventory.findIndex(p => p.id === action.payload.id && p.batchNumber === action.payload.batchNumber);
        if (itemIndex !== -1) {
            draft.inventory[itemIndex] = action.payload.data;
            logActivity('تعديل منتج', `${action.payload.data.name} (${action.payload.data.batchNumber})`);
        }
        break;
      }
      case 'DELETE_INVENTORY_ITEM': {
        const itemToDelete = draft.inventory.find(p => p.id === action.payload.id && p.batchNumber === action.payload.batchNumber);
        if (itemToDelete) logActivity('حذف منتج', `${itemToDelete.name} (${itemToDelete.batchNumber})`);
        draft.inventory = draft.inventory.filter(p => !(p.id === action.payload.id && p.batchNumber === action.payload.batchNumber));
        break;
      }
      case 'ADD_PAYMENT': {
        const { partyType, partyId, partyName, amount } = action.payload;
        if (partyType === 'customer') {
            const customerInvoices = draft.sales
                .filter(s => s.customerId === partyId && (s.status === 'unpaid' || s.status === 'partial'))
                .sort((a,b) => new Date(a.date).getTime() - new Date(b.date).getTime());
            
            let remainingAmountToPay = amount;

            for (const invoice of customerInvoices) {
                if (remainingAmountToPay <= 0) break;
                const due = invoice.totalAmount - invoice.paidAmount;
                const paymentForThisInvoice = Math.min(remainingAmountToPay, due);
                
                invoice.paidAmount += paymentForThisInvoice;
                if(invoice.paidAmount >= invoice.totalAmount) {
                    invoice.status = 'paid';
                } else {
                    invoice.status = 'partial';
                }
                remainingAmountToPay -= paymentForThisInvoice;
            }

            recalculateCustomerBalance(partyId);

            draft.cashboxTransactions.unshift({
                id: Date.now(),
                date: new Date().toISOString().split('T')[0],
                description: `تسديد دفعة من العميل: ${partyName}`,
                type: 'income',
                amount: amount,
            });
        } else { // supplier
            const supplierInvoices = draft.purchases
                .filter(p => p.supplierId === partyId && (p.status === 'unpaid' || p.status === 'partial'))
                .sort((a,b) => new Date(a.date).getTime() - new Date(b.date).getTime());

            let remainingAmountToPay = amount;

            for (const invoice of supplierInvoices) {
                 if (remainingAmountToPay <= 0) break;
                const due = invoice.totalAmount - invoice.paidAmount;
                const paymentForThisInvoice = Math.min(remainingAmountToPay, due);
                
                invoice.paidAmount += paymentForThisInvoice;
                if(invoice.paidAmount >= invoice.totalAmount) {
                    invoice.status = 'paid';
                } else {
                    invoice.status = 'partial';
                }
                remainingAmountToPay -= paymentForThisInvoice;
            }

            recalculateSupplierBalance(partyId);

            draft.cashboxTransactions.unshift({
                id: Date.now(),
                date: new Date().toISOString().split('T')[0],
                description: `تسديد دفعة للمورد: ${partyName}`,
                type: 'expense',
                amount: amount,
            });
        }
        logActivity('تسديد دفعة', `دفعة بقيمة ${amount.toLocaleString()} لـ ${partyName}`);
        break;
      }
      case 'SETTLE_INVOICE': {
          const { invoiceId, type, amount } = action.payload;
          if (type === 'sale') {
              const sale = draft.sales.find(s => s.id === invoiceId);
              if (sale) {
                  sale.paidAmount += amount;
                  
                  if (sale.paidAmount >= sale.totalAmount) {
                      sale.status = 'paid';
                  } else {
                      sale.status = 'partial';
                  }

                  recalculateCustomerBalance(sale.customerId);
                  
                  draft.cashboxTransactions.unshift({
                      id: Date.now(),
                      date: new Date().toISOString().split('T')[0],
                      description: `تحصيل دفعة لفاتورة ${sale.id} من ${sale.customerName}`,
                      type: 'income',
                      amount: amount,
                  });
              }
          } else { // purchase
              const purchase = draft.purchases.find(p => p.id === invoiceId);
              if (purchase) {
                  purchase.paidAmount += amount;

                  if (purchase.paidAmount >= purchase.totalAmount) {
                      purchase.status = 'paid';
                  } else {
                      purchase.status = 'partial';
                  }
                  
                  recalculateSupplierBalance(purchase.supplierId);

                  draft.cashboxTransactions.unshift({
                      id: Date.now(),
                      date: new Date().toISOString().split('T')[0] ,
                      description: `تسديد دفعة لفاتورة ${purchase.id} إلى ${purchase.supplierName}`,
                      type: 'expense',
                      amount: amount,
                  });
              }
          }
           logActivity('تسديد دفعة فاتورة', `دفعة بقيمة ${amount.toLocaleString()} للفاتورة ${invoiceId}`);
          break;
        }
        case 'UPDATE_SETTINGS': {
            draft.settings = { ...draft.settings, ...action.payload };
            logActivity('تحديث الإعدادات', `تم تحديث معلومات الشركة`);
            break;
        }
        case 'SET_SYSTEM_LOGO': {
            draft.settings.systemLogoSvg = action.payload;
            logActivity('تحديث الإعدادات', `تم تغيير شعار النظام`);
            break;
        }
        case 'SET_PRINT_LOGO': {
            draft.settings.printLogoSvg = action.payload;
            logActivity('تحديث الإعدادات', `تم تغيير شعار الطباعة`);
            break;
        }
    }
  });
};

// --- Context and Provider ---

interface ErpContextType {
  state: ErpState;
  dispatch: React.Dispatch<ErpAction>;
}

const ErpContext = createContext<ErpContextType | undefined>(undefined);

const persistenceKey = 'erpState';

let storeForNonReact: {
    getState: () => ErpState
} | null = null;

export const ErpStoreProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(erpReducer, initialState);
  const [isHydrated, setIsHydrated] = React.useState(false);

  storeForNonReact = {
    getState: () => state,
  }

  // Load state from localStorage on initial render
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(persistenceKey);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: 'HYDRATE_STATE', payload: parsedState });
      }
    } catch (e) {
      console.error("Could not load state from localStorage", e);
    }
    setIsHydrated(true);
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (isHydrated) {
        try {
            localStorage.setItem(persistenceKey, JSON.stringify(state));
        } catch (e) {
            console.error("Could not save state to localStorage", e);
        }
    }
  }, [state, isHydrated]);
  

  if (!isHydrated) {
    return null; // Or a loading spinner
  }

  return (
    <ErpContext.Provider value={{ state, dispatch }}>
      {children}
    </ErpContext.Provider>
  );
};


// --- Custom Hook for easy access ---

export const useStore = () => {
  const context = useContext(ErpContext);
  if (context === undefined) {
    if (storeForNonReact) {
        return {
            ...storeForNonReact.getState(),
            addSale: () => {}, deleteSale: () => {}, addPurchase: () => {}, deletePurchase: () => {}, addReturn: () => {}, deleteReturn: () => {},
            addCustomer: () => {}, deleteCustomer: () => {}, addSupplier: () => {}, deleteSupplier: () => {},
            addCashboxTransaction: () => {}, updateCashboxTransaction: () => {}, deleteCashboxTransaction: () => {},
            updateInventoryStock: () => {}, addOrUpdateInventoryItem: () => {}, updateInventoryItem: () => {}, deleteInventoryItem: () => {},
            addPayment: () => {}, settleInvoice: () => {}, updateSettings: () => {}, setSystemLogo: () => {}, setPrintLogo: () => {},
            getState: storeForNonReact.getState
        }
    }
    throw new Error('useStore must be used within an ErpStoreProvider');
  }

  const { state, dispatch } = context;

  // --- Memoized action creators ---
  const addSale = (payload: Omit<SaleRecord, 'status' | 'paidAmount'>) => dispatch({ type: 'ADD_SALE', payload });
  const deleteSale = (payload: string) => dispatch({ type: 'DELETE_SALE', payload });
  const addPurchase = (payload: Omit<PurchaseRecord, 'status' | 'paidAmount'>) => dispatch({ type: 'ADD_PURCHASE', payload });
  const deletePurchase = (payload: string) => dispatch({ type: 'DELETE_PURCHASE', payload });
  const addReturn = (payload: ReturnRecord) => dispatch({ type: 'ADD_RETURN', payload });
  const deleteReturn = (payload: string) => dispatch({ type: 'DELETE_RETURN', payload });
  
  const addCustomer = (payload: Customer) => dispatch({ type: 'ADD_CUSTOMER', payload });
  const deleteCustomer = (payload: number) => dispatch({ type: 'DELETE_CUSTOMER', payload });

  const addSupplier = (payload: Supplier) => dispatch({ type: 'ADD_SUPPLIER', payload });
  const deleteSupplier = (payload: number) => dispatch({ type: 'DELETE_SUPPLIER', payload });

  const addCashboxTransaction = (payload: CashboxTransaction) => dispatch({ type: 'ADD_CASHBOX_TRANSACTION', payload });
  const updateCashboxTransaction = (id: number, data: Partial<CashboxTransaction>) => dispatch({ type: 'UPDATE_CASHBOX_TRANSACTION', payload: {id, data} });
  const deleteCashboxTransaction = (payload: number) => dispatch({ type: 'DELETE_CASHBOX_TRANSACTION', payload });

  const updateInventoryStock = (productId: number, batchNumber: string, change: number) => dispatch({ type: 'UPDATE_INVENTORY_STOCK', payload: { productId, batchNumber, change } });
  const addOrUpdateInventoryItem = (payload: Omit<Product, 'id'> & { id?: number }) => dispatch({ type: 'ADD_OR_UPDATE_INVENTORY_ITEM', payload });
  const updateInventoryItem = (id: number, batchNumber: string, data: Product) => dispatch({ type: 'UPDATE_INVENTORY_ITEM', payload: {id, batchNumber, data }});
  const deleteInventoryItem = (id: number, batchNumber: string) => dispatch({ type: 'DELETE_INVENTORY_ITEM', payload: {id, batchNumber} });
  
  const addPayment = (payload: Payment) => dispatch({ type: 'ADD_PAYMENT', payload });
  const settleInvoice = (invoiceId: string, type: 'sale' | 'purchase', amount: number) => dispatch({ type: 'SETTLE_INVOICE', payload: { invoiceId, type, amount } });
  
  const updateSettings = (payload: Partial<Omit<Settings, 'systemLogoSvg' | 'printLogoSvg'>>) => dispatch({ type: 'UPDATE_SETTINGS', payload });
  const setSystemLogo = (payload: string) => dispatch({ type: 'SET_SYSTEM_LOGO', payload });
  const setPrintLogo = (payload: string) => dispatch({ type: 'SET_PRINT_LOGO', payload });

  return {
    ...state,
    addSale,
    deleteSale,
    addPurchase,
    deletePurchase,
    addReturn,
    deleteReturn,
    addCustomer,
    deleteCustomer,
    addSupplier,
    deleteSupplier,
    addCashboxTransaction,
    updateCashboxTransaction,
    deleteCashboxTransaction,
    updateInventoryStock,
    addOrUpdateInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    addPayment,
    settleInvoice,
    updateSettings,
    setSystemLogo,
    setPrintLogo,
  };
};

// Add a static getState method to useStore for non-React component access
useStore.getState = () => {
    if (!storeForNonReact) {
        // This might happen if called server-side or before provider is mounted.
        // Return initial state as a fallback.
        return initialState;
    }
    return storeForNonReact.getState();
}
