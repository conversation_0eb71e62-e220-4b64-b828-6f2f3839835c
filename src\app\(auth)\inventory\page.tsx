
"use client";

import { useState, use<PERSON>em<PERSON>, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, PlusCircle, Sparkles, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { differenceInDays, parseISO } from 'date-fns';
import { useStore } from "@/store/erp-store";
import type { Product } from "@/store/erp-store";

const LOW_STOCK_THRESHOLD = 20;
const EXPIRING_SOON_DAYS = 60;

type FilterType = "all" | "available" | "low_stock" | "expiring_soon" | "expired";

const initialNewProductState = {
    name: '',
    batchNumber: '',
    stock: 0,
    purchasePrice: 0,
    sellingPrice: 0,
    expDate: '',
};

export default function InventoryPage() {
  const { inventory, sales, addOrUpdateInventoryItem, updateInventoryItem, deleteInventoryItem } = useStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState<FilterType>("all");
  const { toast } = useToast();
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const [newProduct, setNewProduct] = useState(initialNewProductState);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);

  useEffect(() => {
    if (!isEditDialogOpen) {
        setEditingProduct(null);
    }
  }, [isEditDialogOpen]);

  const getStatus = (stock: number, expDate: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Ignore time part
    const expiry = parseISO(expDate);
    const daysUntilExpiry = differenceInDays(expiry, today);

    if (daysUntilExpiry < 0) {
      return { label: "منتهي الصلاحية", variant: "destructive" as const, key: 'expired' as const };
    }
    if (daysUntilExpiry <= EXPIRING_SOON_DAYS) {
      return { label: `تنتهي خلال ${daysUntilExpiry} يوم`, variant: "destructive" as const, key: 'expiring_soon' as const };
    }
    if (stock < LOW_STOCK_THRESHOLD) {
      return { label: "كمية منخفضة", variant: "secondary" as const, key: 'low_stock' as const };
    }
    return { label: "متوفر", variant: "default" as const, key: 'available' as const };
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, isEditing = false) => {
    const { name, value } = e.target;
    const stateSetter = isEditing ? setEditingProduct : setNewProduct;
    stateSetter(prev => prev ? { ...prev, [name]: value } : null);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProduct.name || !newProduct.batchNumber || !newProduct.expDate) {
      toast({
        variant: "destructive",
        title: "حقول مطلوبة",
        description: "يرجى ملء جميع الحقول المطلوبة."
      });
      return;
    }

    const newProductData = {
      ...newProduct,
      stock: Number(newProduct.stock) || 0,
      purchasePrice: Number(newProduct.purchasePrice) || 0,
      sellingPrice: Number(newProduct.sellingPrice) || 0,
    };

    addOrUpdateInventoryItem(newProductData);
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تمت إضافة المنتج "${newProductData.name}" إلى المخزون.`
    });

    setNewProduct(initialNewProductState);
    setIsAddDialogOpen(false);
  };

  const handleEditFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingProduct) return;
    
    const updatedProductData = {
      ...editingProduct,
      stock: Number(editingProduct.stock) || 0,
      purchasePrice: Number(editingProduct.purchasePrice) || 0,
      sellingPrice: Number(editingProduct.sellingPrice) || 0,
    };

    updateInventoryItem(updatedProductData.id, updatedProductData.batchNumber, updatedProductData);
     toast({
      title: "تم التعديل بنجاح",
      description: `تم تحديث بيانات المنتج "${updatedProductData.name}".`
    });
    setIsEditDialogOpen(false);
  };

  const handleDelete = (item: Product) => {
    deleteInventoryItem(item.id, item.batchNumber);
    toast({
        title: "تم الحذف بنجاح",
        description: `تم حذف "${item.name} (${item.batchNumber})".`
    });
  }

  const openEditDialog = (item: Product) => {
    setEditingProduct(item);
    setIsEditDialogOpen(true);
  }

  const filteredInventory = useMemo(() => {
    return inventory
        .filter(item =>
            item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.batchNumber.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .filter(item => {
            if (activeFilter === 'all') return true;
            const status = getStatus(item.stock, item.expDate);
            return status.key === activeFilter;
        });
  }, [searchTerm, inventory, activeFilter]);

  return (
    <>
      <PageHeader
        title="إدارة المخزون"
        description="عرض المخزون المتاح وصلاحية الأدوية وتنبيهات الكمية."
        action={
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                    <Button>
                        <PlusCircle className="ml-2 h-4 w-4" />
                        إضافة منتج جديد
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>إضافة منتج جديد للمخزون</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleFormSubmit}>
                        <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">اسم المنتج</Label>
                                <Input id="name" name="name" value={newProduct.name} onChange={(e) => handleInputChange(e)} placeholder="مثال: بنادول اكسترا" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="batchNumber">رقم الوجبة</Label>
                                <Input id="batchNumber" name="batchNumber" value={newProduct.batchNumber} onChange={(e) => handleInputChange(e)} placeholder="B123" />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="stock">الكمية</Label>
                                    <Input id="stock" name="stock" type="number" value={newProduct.stock || ''} onChange={(e) => handleInputChange(e)} placeholder="0" />
                                </div>
                                 <div className="space-y-2">
                                    <Label htmlFor="expDate">تاريخ الانتهاء</Label>
                                    <Input id="expDate" name="expDate" type="date" value={newProduct.expDate} onChange={(e) => handleInputChange(e)} />
                                </div>
                            </div>
                             <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="purchasePrice">سعر الشراء</Label>
                                    <Input id="purchasePrice" name="purchasePrice" type="number" value={newProduct.purchasePrice || ''} onChange={(e) => handleInputChange(e)} placeholder="0" />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="sellingPrice">سعر البيع</Label>
                                    <Input id="sellingPrice" name="sellingPrice" type="number" value={newProduct.sellingPrice || ''} onChange={(e) => handleInputChange(e)} placeholder="0" />
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">إلغاء</Button>
                            </DialogClose>
                            <Button type="submit">حفظ المنتج</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        }
      />
      <Card>
        <CardHeader>
          <CardTitle>قائمة المخزون</CardTitle>
          <CardDescription>
            ابحث عن منتج أو قم بتصفية المخزون لعرض المواد المطلوبة.
          </CardDescription>
           <div className="pt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Input 
                placeholder="بحث حسب اسم المنتج أو رقم الوجبة..." 
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="sm:col-span-2"
              />
               <Select value={activeFilter} onValueChange={(value) => setActiveFilter(value as FilterType)}>
                <SelectTrigger>
                    <SelectValue placeholder="تصفية حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">عرض الكل</SelectItem>
                    <SelectItem value="available">متوفر</SelectItem>
                    <SelectItem value="low_stock">كمية منخفضة</SelectItem>
                    <SelectItem value="expiring_soon">قرب انتهاء الصلاحية</SelectItem>
                    <SelectItem value="expired">منتهي الصلاحية</SelectItem>
                </SelectContent>
                </Select>
           </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>اسم المنتج</TableHead>
                <TableHead>الوجبة</TableHead>
                <TableHead>الكمية</TableHead>
                <TableHead>سعر البيع</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead className="text-right">إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInventory.length === 0 ? (
                <TableRow>
                    <TableCell colSpan={8} className="text-center h-24">
                        لم يتم العثور على نتائج.
                    </TableCell>
                </TableRow>
              ) : (
                filteredInventory.map((item) => {
                    const status = getStatus(item.stock, item.expDate);
                    const analysisKey = `${item.id}-${item.batchNumber}`;
                    return (
                        <TableRow key={analysisKey}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell>{item.batchNumber}</TableCell>
                            <TableCell>{item.stock}</TableCell>
                            <TableCell>{item.sellingPrice.toLocaleString()} د.ع</TableCell>
                            <TableCell>
                                <Badge variant={status.variant}>{status.label}</Badge>
                            </TableCell>
                            <TableCell className="text-right">
                                <AlertDialog>
                                    <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                        <span className="sr-only">فتح القائمة</span>
                                        <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
                                        <DropdownMenuItem onClick={() => openEditDialog(item)}>تعديل المنتج</DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <AlertDialogTrigger asChild>
                                            <DropdownMenuItem className="text-destructive">حذف المنتج</DropdownMenuItem>
                                        </AlertDialogTrigger>
                                    </DropdownMenuContent>
                                    </DropdownMenu>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                        <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                                        <AlertDialogDescription>
                                            هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المنتج "{item.name}" (الوجبة: {item.batchNumber}) بشكل دائم.
                                        </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                        <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                        <AlertDialogAction onClick={() => handleDelete(item)}>متابعة</AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </TableCell>
                        </TableRow>
                    )
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
            <DialogHeader>
                <DialogTitle>تعديل بيانات المنتج</DialogTitle>
            </DialogHeader>
            {editingProduct && (
                 <form onSubmit={handleEditFormSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="edit-name">اسم المنتج</Label>
                            <Input id="edit-name" name="name" value={editingProduct.name} onChange={(e) => handleInputChange(e, true)} />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                             <div className="space-y-2">
                                <Label htmlFor="edit-batchNumber">رقم الوجبة</Label>
                                <Input id="edit-batchNumber" name="batchNumber" value={editingProduct.batchNumber} disabled />
                            </div>
                             <div className="space-y-2">
                                <Label htmlFor="edit-expDate">تاريخ الانتهاء</Label>
                                <Input id="edit-expDate" name="expDate" type="date" value={editingProduct.expDate} onChange={(e) => handleInputChange(e, true)} />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="edit-purchasePrice">سعر الشراء</Label>
                                <Input id="edit-purchasePrice" name="purchasePrice" type="number" value={editingProduct.purchasePrice || ''} onChange={(e) => handleInputChange(e, true)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="edit-sellingPrice">سعر البيع</Label>
                                <Input id="edit-sellingPrice" name="sellingPrice" type="number" value={editingProduct.sellingPrice || ''} onChange={(e) => handleInputChange(e, true)} />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-stock">الكمية (للتعديل اليدوي)</Label>
                            <Input id="edit-stock" name="stock" type="number" value={editingProduct.stock || ''} onChange={(e) => handleInputChange(e, true)} />
                        </div>
                    </div>
                    <DialogFooter>
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">إلغاء</Button>
                        </DialogClose>
                        <Button type="submit">حفظ التغييرات</Button>
                    </DialogFooter>
                </form>
            )}
        </DialogContent>
      </Dialog>
    </>
  );
}
