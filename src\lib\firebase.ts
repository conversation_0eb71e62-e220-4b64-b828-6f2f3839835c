
import { initializeApp, getApp, getApps } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAuth } from 'firebase/auth';

// Your web app's Firebase configuration
const firebaseConfig = {
  projectId: "laren-accounting-vkyo8",
  appId: "1:************:web:9121a9a3ed777858d78e57",
  storageBucket: "laren-accounting-vkyo8.firebasestorage.app",
  apiKey: "AIzaSyAwx2oA0ua3RnidAIzhkm5u8pjWuI_NVOA",
  authDomain: "laren-accounting-vkyo8.firebaseapp.com",
  messagingSenderId: "************",
  measurementId: ""
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const db = getFirestore(app);
const storage = getStorage(app);
const auth = getAuth(app);

export { app, db, storage, auth };
