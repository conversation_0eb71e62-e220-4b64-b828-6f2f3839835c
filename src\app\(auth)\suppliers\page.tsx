
"use client";

import { useState } from "react";
import { useRouter } from 'next/navigation';
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, PlusCircle, Sparkles, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useStore } from "@/store/erp-store";
import type { Supplier } from "@/store/erp-store";


const initialNewSupplierState = {
    name: '',
    phone: '',
    address: '',
};

export default function SuppliersPage() {
    const { 
        suppliers,
        purchases, 
        addSupplier, 
        deleteSupplier, 
        addPayment,
    } = useStore();
    const router = useRouter();

    const [newSupplier, setNewSupplier] = useState(initialNewSupplierState);
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
    const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
    const [paymentAmount, setPaymentAmount] = useState(0);
    const { toast } = useToast();
    
     const viewAccountStatement = (supplier: Supplier) => {
        const query = new URLSearchParams({
            reportType: 'account',
            accountId: `supplier-${supplier.id}`
        }).toString();
        router.push(`/reports?${query}`);
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setNewSupplier(prev => ({ ...prev, [name]: value }));
    };

    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newSupplier.name || !newSupplier.phone) {
            toast({
                variant: "destructive",
                title: "حقول مطلوبة",
                description: "يرجى إدخال اسم المورد ورقم هاتفه على الأقل."
            });
            return;
        }

        const newSupplierData = {
            id: Date.now(),
            ...newSupplier,
            balance: 0,
        };

        addSupplier(newSupplierData);
        toast({
            title: "تمت الإضافة بنجاح",
            description: `تمت إضافة المورد "${newSupplierData.name}" إلى القائمة.`
        });

        setNewSupplier(initialNewSupplierState);
        setIsAddDialogOpen(false);
    };
    
    const handleDelete = (supplierId: number, supplierName: string) => {
        deleteSupplier(supplierId);
        toast({
            title: "تم الحذف بنجاح",
            description: `تم حذف المورد "${supplierName}" من القائمة.`
        });
    }

    const openPaymentDialog = (supplier: Supplier) => {
        setSelectedSupplier(supplier);
        setIsPaymentDialogOpen(true);
        setPaymentAmount(0);
    };

    const handlePaymentSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedSupplier || paymentAmount <= 0) {
            toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء إدخال مبلغ صحيح.' });
            return;
        }

        addPayment({
            partyType: 'supplier',
            partyId: selectedSupplier.id,
            partyName: selectedSupplier.name,
            amount: paymentAmount,
        });

        toast({
            title: 'تم تسجيل الدفعة',
            description: `تم تسجيل دفعة بقيمة ${paymentAmount.toLocaleString()} د.ع للمورد ${selectedSupplier.name}. تم تحديث صندوق النقد والفواتير.`
        });

        setIsPaymentDialogOpen(false);
        setSelectedSupplier(null);
    };

  return (
    <>
      <PageHeader
        title="إدارة الموردين"
        description="عرض وتعديل بيانات الموردين وكشوفات حساباتهم."
        action={
           <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                    <Button>
                        <PlusCircle className="ml-2 h-4 w-4" />
                        إضافة مورد جديد
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                     <DialogHeader>
                        <DialogTitle>إضافة مورد جديد</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleFormSubmit}>
                        <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">اسم المورد</Label>
                                <Input id="name" name="name" value={newSupplier.name} onChange={handleInputChange} placeholder="مثال: مذخر الخيرات" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phone">رقم الهاتف</Label>
                                <Input id="phone" name="phone" value={newSupplier.phone} onChange={handleInputChange} placeholder="07XX XXX XXXX" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="address">العنوان</Label>
                                <Input id="address" name="address" value={newSupplier.address} onChange={handleInputChange} placeholder="مثال: البصرة - العشار" />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">إلغاء</Button>
                            </DialogClose>
                            <Button type="submit">حفظ المورد</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        }
      />
      <Card>
        <CardHeader>
           <CardTitle>قائمة الموردين</CardTitle>
           <CardDescription>
              ابحث عن مورد أو قم بإدارة الموردين الحاليين.
           </CardDescription>
            <div className="pt-4">
              <Input placeholder="بحث عن مورد..." />
           </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>اسم المورد</TableHead>
                <TableHead>رقم الهاتف</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead>الرصيد</TableHead>
                <TableHead className="text-right">إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suppliers.map((supplier) => (
                <TableRow key={supplier.id}>
                  <TableCell className="font-medium">{supplier.name}</TableCell>
                  <TableCell>{supplier.phone}</TableCell>
                  <TableCell>{supplier.address}</TableCell>
                  <TableCell>
                     <Badge 
                        variant={supplier.balance > 0 ? 'destructive' : supplier.balance < 0 ? 'secondary' : 'default'}
                     >
                        {supplier.balance.toLocaleString('ar-IQ')} د.ع
                     </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                     <AlertDialog>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">فتح القائمة</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
                            <DropdownMenuItem onSelect={() => viewAccountStatement(supplier)}>عرض الكشف</DropdownMenuItem>
                            <DropdownMenuItem>تعديل البيانات</DropdownMenuItem>
                            <DropdownMenuItem onSelect={() => openPaymentDialog(supplier)}>تسديد دفعة</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialogTrigger asChild>
                                <DropdownMenuItem className="text-destructive">حذف المورد</DropdownMenuItem>
                            </AlertDialogTrigger>
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <AlertDialogContent>
                            <AlertDialogHeader>
                            <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                            <AlertDialogDescription>
                                هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف بيانات المورد "{supplier.name}" بشكل دائم.
                            </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                            <AlertDialogCancel>إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(supplier.id, supplier.name)}>متابعة</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Payment Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>تسجيل دفعة لمورد</DialogTitle>
                </DialogHeader>
                {selectedSupplier && (
                    <form onSubmit={handlePaymentSubmit}>
                        <div className="grid gap-4 py-4">
                            <p>تسجيل دفعة للمورد: <span className="font-bold">{selectedSupplier.name}</span></p>
                             <p>الرصيد الحالي: <span className="font-bold">{selectedSupplier.balance.toLocaleString('ar-IQ')} د.ع</span></p>
                            <div className="space-y-2">
                                <Label htmlFor="payment-amount">مبلغ الدفعة</Label>
                                <Input
                                    id="payment-amount"
                                    type="number"
                                    value={paymentAmount || ''}
                                    onChange={(e) => setPaymentAmount(Number(e.target.value))}
                                    placeholder="أدخل المبلغ"
                                    required
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button type="button" variant="secondary">إلغاء</Button>
                            </DialogClose>
                            <Button type="submit">حفظ الدفعة</Button>
                        </DialogFooter>
                    </form>
                )}
            </DialogContent>
        </Dialog>
    </>
  );
}
