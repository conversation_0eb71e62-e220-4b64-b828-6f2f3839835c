
"use client";

import { useState } from "react";
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import { ArrowLeft, Save, PlusCircle, Trash2, Bot } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

type Permission = 'read' | 'create' | 'update' | 'delete';
type Module = 'dashboard' | 'sales' | 'purchases' | 'returns' | 'customers' | 'suppliers' | 'inventory' | 'cashbox' | 'reports' | 'settings' | 'activityLog';

type RolePermissions = Record<Module, Record<Permission, boolean>>;

const modulesAr: Record<Module, string> = {
    dashboard: "لوحة التحكم",
    sales: "المبيعات",
    purchases: "المشتريات",
    returns: "المرتجعات",
    customers: "العملاء",
    suppliers: "الموردون",
    inventory: "المخزون",
    cashbox: "صندوق النقد",
    reports: "التقارير",
    settings: "الإعدادات",
    activityLog: "سجل الأنشطة",
};

const defaultPermissions: Record<Permission, boolean> = { read: false, create: false, update: false, delete: false };
const allPermissions: Record<Permission, boolean> = { read: true, create: true, update: true, delete: true };


const initialPermissions: Record<string, RolePermissions> = {
    'مدير': Object.fromEntries(Object.keys(modulesAr).map(m => [m, {...allPermissions}])) as RolePermissions,
    'محاسب': {
        dashboard: { read: true, create: false, update: false, delete: false },
        sales: { read: true, create: true, update: true, delete: false },
        purchases: { read: true, create: true, update: true, delete: false },
        returns: { read: true, create: true, update: true, delete: false },
        customers: { read: true, create: true, update: true, delete: false },
        suppliers: { read: true, create: true, update: true, delete: false },
        inventory: { read: true, create: false, update: false, delete: false },
        cashbox: { read: true, create: true, update: true, delete: false },
        reports: { read: true, create: false, update: false, delete: false },
        settings: { read: false, create: false, update: false, delete: false },
        activityLog: { read: true, create: false, update: false, delete: false },
    },
    'بائع': {
        dashboard: { read: true, create: false, update: false, delete: false },
        sales: { read: true, create: true, update: false, delete: false },
        purchases: { read: false, create: false, update: false, delete: false },
        returns: { read: true, create: true, update: false, delete: false },
        customers: { read: true, create: true, update: true, delete: false },
        suppliers: { read: false, create: false, update: false, delete: false },
        inventory: { read: true, create: false, update: false, delete: false },
        cashbox: { read: false, create: false, update: false, delete: false },
        reports: { read: false, create: false, update: false, delete: false },
        settings: { read: false, create: false, update: false, delete: false },
        activityLog: { read: false, create: false, update: false, delete: false },
    },
};

export default function RolesPage() {
    const [permissions, setPermissions] = useState(initialPermissions);
    const [newRoleName, setNewRoleName] = useState("");
    const { toast } = useToast();

    const handlePermissionChange = (role: string, module: Module, permission: Permission, checked: boolean) => {
        setPermissions(prev => ({
            ...prev,
            [role]: {
                ...prev[role],
                [module]: {
                    ...prev[role][module],
                    [permission]: checked
                }
            }
        }));
    };

    const handleSave = (role: string) => {
        console.log(`Saving permissions for role: ${role}`, permissions[role]);
        toast({
            title: "تم الحفظ بنجاح",
            description: `تم تحديث صلاحيات دور "${role}".`
        });
    };
    
    const handleAddRole = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newRoleName.trim()) {
            toast({ variant: 'destructive', title: 'خطأ', description: 'الرجاء إدخال اسم للدور الجديد.' });
            return;
        }
        if (permissions[newRoleName.trim()]) {
             toast({ variant: 'destructive', title: 'خطأ', description: 'هذا الدور موجود بالفعل.' });
            return;
        }

        const newRoleKey = newRoleName.trim();
        const newRolePermissions: RolePermissions = Object.fromEntries(
            Object.keys(modulesAr).map(module => [module, { ...defaultPermissions }])
        ) as RolePermissions;

        setPermissions(prev => ({
            ...prev,
            [newRoleKey]: newRolePermissions,
        }));
        
        setNewRoleName("");
        toast({ title: 'تمت الإضافة', description: `تمت إضافة دور "${newRoleKey}" بنجاح.` });
    };

    const handleDeleteRole = (role: string) => {
        if (role === 'مدير' || role === 'محاسب' || role === 'بائع') {
            toast({ variant: 'destructive', title: 'خطأ', description: 'لا يمكن حذف الأدوار الأساسية.' });
            return;
        }
        const newPermissions = { ...permissions };
        delete newPermissions[role];
        setPermissions(newPermissions);
        toast({ title: 'تم الحذف', description: `تم حذف دور "${role}" بنجاح.` });
    };

    return (
        <>
            <PageHeader
                title="إدارة الأدوار والصلاحيات"
                description="تحكم في صلاحيات الوصول لكل دور في النظام (عرض، إضافة، تعديل، حذف)."
                action={
                    <Dialog>
                        <DialogTrigger asChild>
                             <Button>
                                <PlusCircle className="ml-2 h-4 w-4" />
                                إضافة دور جديد
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                             <DialogHeader>
                                <DialogTitle>إضافة دور جديد</DialogTitle>
                             </DialogHeader>
                             <form onSubmit={handleAddRole}>
                                 <div className="py-4">
                                     <Label htmlFor="new-role-name">اسم الدور</Label>
                                     <Input 
                                        id="new-role-name"
                                        value={newRoleName}
                                        onChange={(e) => setNewRoleName(e.target.value)}
                                        placeholder="مثال: مدخل بيانات"
                                     />
                                 </div>
                                <DialogFooter>
                                    <DialogClose asChild><Button variant="secondary" type="button">إلغاء</Button></DialogClose>
                                    <DialogClose asChild><Button type="submit">إضافة</Button></DialogClose>
                                </DialogFooter>
                             </form>
                        </DialogContent>
                    </Dialog>
                }
            />
            <div className="mb-4">
                <Link href="/settings" className="inline-flex items-center text-sm text-primary hover:underline">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    <span>العودة إلى الإعدادات</span>
                </Link>
            </div>

            <Accordion type="single" collapsible className="w-full space-y-4">
                {Object.entries(permissions).map(([role, roleData]) => (
                    <AccordionItem value={role} key={role} className="border-none">
                         <Card>
                            <AccordionTrigger className="p-6 hover:no-underline">
                                <CardHeader className="p-0 flex-row items-center justify-between text-right w-full">
                                    <div>
                                        <CardTitle>{role}</CardTitle>
                                        <CardDescription>انقر لعرض وتعديل صلاحيات هذا الدور.</CardDescription>
                                    </div>
                                </CardHeader>
                            </AccordionTrigger>
                             <AccordionContent>
                                <CardContent className="space-y-8">
                                    <div>
                                        <h4 className="text-lg font-semibold mb-4">صلاحيات النظام</h4>
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>الشاشة / الوحدة</TableHead>
                                                    <TableHead className="text-center">عرض</TableHead>
                                                    <TableHead className="text-center">إضافة</TableHead>
                                                    <TableHead className="text-center">تعديل</TableHead>
                                                    <TableHead className="text-center">حذف</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {Object.entries(roleData).map(([module, perms]) => (
                                                    <TableRow key={module}>
                                                        <TableCell className="font-medium">{modulesAr[module as Module]}</TableCell>
                                                        {(Object.keys(perms) as Permission[]).map(pKey => (
                                                            <TableCell key={pKey} className="text-center">
                                                                <Checkbox
                                                                    checked={perms[pKey]}
                                                                    onCheckedChange={(checked) => handlePermissionChange(role, module as Module, pKey, !!checked)}
                                                                />
                                                            </TableCell>
                                                        ))}
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </CardContent>
                                <CardFooter className="flex justify-between">
                                    <Button onClick={() => handleSave(role)}>
                                        <Save className="ml-2 h-4 w-4" />
                                        حفظ التغييرات لدور "{role}"
                                    </Button>
                                    {role !== 'مدير' && role !== 'محاسب' && role !== 'بائع' && (
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                             <Button variant="destructive">
                                                <Trash2 className="ml-2 h-4 w-4"/>
                                                حذف هذا الدور
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>هل أنت متأكد من حذف دور "{role}"؟</AlertDialogTitle>
                                                <AlertDialogDescription>هذا الإجراء لا يمكن التراجع عنه.</AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDeleteRole(role)}>متابعة الحذف</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                    )}
                                </CardFooter>
                             </AccordionContent>
                         </Card>
                    </AccordionItem>
                ))}
            </Accordion>
        </>
    );
}
