
"use client";

import { useState } from "react";
import { useStore, type ErpState } from "@/store/erp-store";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Download, Upload, AlertTriangle, FileJson, FileSpreadsheet, HardDriveDownload, HardDriveUpload } from "lucide-react";
import * as XLSX from 'xlsx';
import { Input } from "@/components/ui/input";

export function BackupRestore() {
    const store = useStore();
    const { toast } = useToast();
    const [fileToRestore, setFileToRestore] = useState<File | null>(null);

    const handleBackup = () => {
        try {
            const stateJson = JSON.stringify(store, null, 2);
            const blob = new Blob([stateJson], { type: "application/json" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            const date = new Date().toISOString().split('T')[0];
            link.href = url;
            link.download = `erp-backup-${date}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            toast({
                title: "تم إنشاء النسخة الاحتياطية",
                description: "تم تنزيل ملف النسخة الاحتياطية بنجاح.",
            });
        } catch (error) {
            console.error("Backup failed:", error);
            toast({
                variant: "destructive",
                title: "فشل النسخ الاحتياطي",
                description: "حدث خطأ أثناء إنشاء ملف النسخة الاحتياطية.",
            });
        }
    };
    
    const handleExportToExcel = (data: any[], fileName: string, sheetName: string) => {
        try {
            if (data.length === 0) {
                toast({ variant: 'destructive', title: 'لا توجد بيانات', description: 'لا توجد بيانات لتصديرها.' });
                return;
            }
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
            XLSX.writeFile(workbook, `${fileName}.xlsx`);
            toast({ title: 'تم التصدير بنجاح', description: `تم تصدير ${fileName}.xlsx` });
        } catch(error) {
            console.error("Excel export failed:", error);
            toast({ variant: 'destructive', title: 'فشل التصدير', description: 'حدث خطأ أثناء تصدير ملف Excel.' });
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file && file.type === "application/json") {
            setFileToRestore(file);
        } else {
            toast({
                variant: "destructive",
                title: "ملف غير صالح",
                description: "الرجاء تحديد ملف JSON صالح.",
            });
            setFileToRestore(null);
        }
    };

    const handleRestore = () => {
        if (!fileToRestore) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const text = e.target?.result;
                if (typeof text !== 'string') throw new Error("File content is not a string");
                
                const restoredState: ErpState = JSON.parse(text);
                
                // A simple validation to check if it's a valid state file
                if ('inventory' in restoredState && 'customers' in restoredState && 'settings' in restoredState) {
                    // In a real app, you'd dispatch an action to replace the state
                    // For now, we'll just log it and show a success message
                    console.log("Restored State:", restoredState);
                    localStorage.setItem('erpState', JSON.stringify(restoredState));

                    toast({
                        title: "نجاح الاستعادة!",
                        description: "تم استعادة البيانات بنجاح. يرجى إعادة تحميل الصفحة لتطبيق التغييرات.",
                        duration: 7000,
                    });
                } else {
                    throw new Error("Invalid state file format.");
                }
            } catch (error) {
                console.error("Restore failed:", error);
                toast({
                    variant: "destructive",
                    title: "فشل الاستعادة",
                    description: "الملف المحدد غير صالح أو تالف. يرجى التحقق من الملف والمحاولة مرة أخرى.",
                });
            }
        };
        reader.readAsText(fileToRestore);
    };

    return (
        <div className="grid gap-6">
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-3">
                        <HardDriveDownload className="h-6 w-6 text-primary" />
                        <div>
                            <CardTitle>النسخ الاحتياطي للبيانات</CardTitle>
                            <CardDescription>
                                قم بإنشاء نسخ احتياطية من بيانات نظامك لحمايتها.
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2"><FileJson className="h-5 w-5"/> نسخة احتياطية كاملة (JSON)</h4>
                        <p className="text-sm text-muted-foreground mb-3">
                            يقوم هذا الخيار بإنشاء ملف واحد يحتوي على جميع بيانات النظام (الفواتير، المخزون، العملاء، الإعدادات، إلخ). استخدم هذا الملف لاستعادة النظام بالكامل.
                        </p>
                        <Button onClick={handleBackup}>
                            <Download className="ml-2 h-4 w-4" />
                            تنزيل النسخة الاحتياطية الكاملة
                        </Button>
                    </div>
                    <Separator />
                    <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2"><FileSpreadsheet className="h-5 w-5"/> تصدير إلى Excel</h4>
                         <p className="text-sm text-muted-foreground mb-3">
                            قم بتصدير أجزاء محددة من بياناتك إلى ملفات Excel لسهولة العرض والتحليل.
                        </p>
                        <div className="flex flex-wrap gap-2">
                            <Button variant="outline" onClick={() => handleExportToExcel(store.inventory, 'inventory', 'المخزون')}>تصدير المخزون</Button>
                            <Button variant="outline" onClick={() => handleExportToExcel(store.sales, 'sales', 'المبيعات')}>تصدير المبيعات</Button>
                            <Button variant="outline" onClick={() => handleExportToExcel(store.purchases, 'purchases', 'المشتريات')}>تصدير المشتريات</Button>
                            <Button variant="outline" onClick={() => handleExportToExcel(store.customers, 'customers', 'العملاء')}>تصدير العملاء</Button>
                            <Button variant="outline" onClick={() => handleExportToExcel(store.suppliers, 'suppliers', 'الموردون')}>تصدير الموردين</Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card>
                 <CardHeader>
                    <div className="flex items-center gap-3">
                        <HardDriveUpload className="h-6 w-6 text-destructive" />
                        <div>
                            <CardTitle>استعادة البيانات</CardTitle>
                            <CardDescription>
                                قم باستعادة البيانات من ملف نسخة احتياطية.
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>تحذير هام</AlertTitle>
                        <AlertDescription>
                            عملية الاستعادة ستقوم بـ <strong>حذف جميع البيانات الحالية</strong> واستبدالها بالبيانات الموجودة في ملف النسخة الاحتياطية. لا يمكن التراجع عن هذا الإجراء.
                        </AlertDescription>
                    </Alert>
                    <div className="space-y-2">
                        <label htmlFor="restore-file" className="block text-sm font-medium text-gray-700">اختر ملف النسخة الاحتياطية (.json)</label>
                        <Input
                            id="restore-file"
                            type="file"
                            accept="application/json"
                            onChange={handleFileChange}
                        />
                    </div>
                    {fileToRestore && (
                        <p className="text-sm text-muted-foreground">الملف المحدد: {fileToRestore.name}</p>
                    )}
                </CardContent>
                <CardFooter>
                    <AlertDialog>
                        <AlertDialogTrigger asChild>
                            <Button variant="destructive" disabled={!fileToRestore}>
                                <Upload className="ml-2 h-4 w-4" />
                                استعادة البيانات الآن
                            </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                            <AlertDialogHeader>
                                <AlertDialogTitle>هل أنت متأكد تماماً؟</AlertDialogTitle>
                                <AlertDialogDescription>
                                    أنت على وشك استبدال جميع بياناتك الحالية. هذا الإجراء نهائي ولا يمكن التراجع عنه. هل ترغب في المتابعة؟
                                </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                <AlertDialogAction onClick={handleRestore}>نعم، قم بالاستعادة</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                </CardFooter>
            </Card>
        </div>
    );
}
