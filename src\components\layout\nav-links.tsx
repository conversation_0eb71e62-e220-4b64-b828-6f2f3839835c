
"use client";

import { usePathname } from "next/navigation";
import {
  BarChart3,
  Building,
  Boxes,
  Settings,
  Truck,
  Undo2,
  Users,
  Wallet,
} from "lucide-react";
import {
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import {
  ModernDashboardIcon,
  ModernInventoryIcon,
  ModernSalesIcon,
  ModernCustomersIcon,
  ModernReportsIcon,
  ModernCashboxIcon,
  ModernSettingsIcon
} from "@/components/icons/modern-icons";

const links = [
  { href: "/dashboard", label: "لوحة التحكم", icon: ModernDashboardIcon },
  { href: "/sales", label: "المبيعات", icon: ModernSalesIcon },
  { href: "/purchases", label: "المشتريات", icon: Truck },
  { href: "/returns", label: "المرتجعات", icon: Undo2 },
  { href: "/customers", label: "العملاء", icon: ModernCustomersIcon },
  { href: "/suppliers", label: "الموردون", icon: Building },
  { href: "/inventory", label: "المخزون", icon: ModernInventoryIcon },
  { href: "/cashbox", label: "صندوق النقد", icon: ModernCashboxIcon },
  { href: "/reports", label: "التقارير", icon: ModernReportsIcon },
  { href: "/settings", label: "الإعدادات", icon: ModernSettingsIcon },
];

export default function NavLinks() {
  const pathname = usePathname();

  return (
    <>
      {links.map((link) => (
        <SidebarMenuItem key={link.href}>
          <SidebarMenuButton
            asChild
            isActive={pathname.startsWith(link.href) && (link.href !== '/settings' || pathname === '/settings')}
            className="justify-start"
            tooltip={link.label}
          >
            <a href={link.href} className="flex items-center gap-3 hover-lift">
              <link.icon className="h-5 w-5" />
              <span className="font-medium">{link.label}</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </>
  );
}

    