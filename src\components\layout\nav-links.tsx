
"use client";

import { usePathname } from "next/navigation";
import {
  BarChart3,
  Building,
  Boxes,
  LayoutDashboard,
  Settings,
  ShoppingCart,
  Truck,
  Undo2,
  Users,
  Wallet,
} from "lucide-react";
import {
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

const links = [
  { href: "/dashboard", label: "لوحة التحكم", icon: LayoutDashboard },
  { href: "/sales", label: "المبيعات", icon: ShoppingCart },
  { href: "/purchases", label: "المشتريات", icon: Truck },
  { href: "/returns", label: "المرتجعات", icon: Undo2 },
  { href: "/customers", label: "العملاء", icon: Users },
  { href: "/suppliers", label: "الموردون", icon: Building },
  { href: "/inventory", label: "المخزون", icon: Boxes },
  { href: "/cashbox", label: "صندوق النقد", icon: Wallet },
  { href: "/reports", label: "التقارير", icon: BarChart3 },
  { href: "/settings", label: "الإعدادات", icon: Settings },
];

export default function NavLinks() {
  const pathname = usePathname();

  return (
    <>
      {links.map((link) => (
        <SidebarMenuItem key={link.href}>
          <SidebarMenuButton
            asChild
            isActive={pathname.startsWith(link.href) && (link.href !== '/settings' || pathname === '/settings')}
            className="justify-start"
            tooltip={link.label}
          >
            <a href={link.href}>
              <link.icon className="h-4 w-4" />
              <span>{link.label}</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </>
  );
}

    