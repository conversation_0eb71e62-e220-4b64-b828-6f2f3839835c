
'use client';

import React from 'react';
import { Logo } from '@/components/icons';
import { Separator } from '@/components/ui/separator';

interface InvoiceItem {
  seq: number;
  name: string;
  quantity: number;
  bonus: number;
  price: number;
  exp: string;
  notes: string;
}

export interface InvoiceTemplateProps {
  invoiceNumber: string;
  issueDate: string;
  invoiceType: string;
  paymentMethod: string;
  region: string;
  customerName: string;
  items: InvoiceItem[];
  discount: number;
  totalReturnedAmount?: number;
  notes: string;
  companyInfo: {
    name: string;
    description: string;
    address: string;
  };
  printLogoSvg: string | null;
}


export function InvoiceTemplate({
    invoiceNumber,
    issueDate,
    invoiceType,
    paymentMethod,
    region,
    customerName,
    items,
    discount,
    totalReturnedAmount,
    notes,
    companyInfo,
    printLogoSvg
}: InvoiceTemplateProps) {

  const subtotal = items.reduce((acc, item) => acc + (item.quantity * item.price), 0);
  const totalBonusValue = items.reduce((acc, item) => acc + (item.bonus * item.price), 0);
  const financialTotal = subtotal - discount;

  // For display purposes, the grand total might be different
  // For a sales invoice, the bonus value is part of the discount
  const displayTotal = totalReturnedAmount ? totalReturnedAmount : financialTotal; 

  return (
    <div className="invoice-container bg-card text-card-foreground p-4 sm:p-8">
      <header className="flex flex-col sm:flex-row justify-between items-start pb-4 border-b-2 border-black gap-4">
        <div className="text-center sm:text-right flex-grow">
            <h1 className="text-xl sm:text-2xl font-bold text-primary">{companyInfo.name}</h1>
            <p className="text-xs sm:text-sm">{companyInfo.description}</p>
            <p className="text-xs sm:text-sm mt-2">{companyInfo.address}</p>
        </div>
        <div className="w-32 h-32 flex items-center justify-center self-center sm:self-start shrink-0">
           {printLogoSvg ? (
                <img 
                    src={`data:image/svg+xml;base64,${btoa(printLogoSvg)}`} 
                    alt="Company Logo"
                    className="w-full h-full object-contain"
                />
            ) : (
                <Logo className="w-full h-full text-primary" />
            )}
        </div>
      </header>

      <section className="grid grid-cols-1 sm:grid-cols-3 gap-4 my-4 text-xs sm:text-sm">
        <div className="space-y-1">
            <p><strong>الرقم:</strong> {invoiceNumber}</p>
            <p><strong>تاريخ الإصدار:</strong> {issueDate}</p>
        </div>
        <div className="space-y-1">
            <p><strong>النوع:</strong> <span className="font-bold">{invoiceType}</span></p>
            <p><strong>طريقة الدفع:</strong> {paymentMethod}</p>
        </div>
         <div className="space-y-1">
            <p><strong>{invoiceType.includes('شراء') ? 'اسم المورد:' : 'اسم الزبون:'}</strong> {customerName}</p>
            {region && <p><strong>المنطقة:</strong> {region}</p>}
        </div>
      </section>

      <Separator className="my-4 bg-gray-400" />

      <main>
        <div className="overflow-x-auto">
            <table className="w-full text-sm text-right">
              <thead className="border-b-2 border-black">
                <tr>
                  <th className="p-2 text-center">ت</th>
                  <th className="p-2 min-w-[150px]">اسم المادة</th>
                  <th className="p-2 text-center">الكمية</th>
                  <th className="p-2 text-center">بونص</th>
                  <th className="p-2 text-center">ت. الانتهاء</th>
                  <th className="p-2 text-center">السعر</th>
                  <th className="p-2 text-left">الإجمالي</th>
                  <th className="p-2 text-center">ملاحظات</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => (
                  <tr key={item.seq} className="border-b border-gray-300">
                    <td className="p-2 text-center">{item.seq}</td>
                    <td className="p-2 font-medium">{item.name}</td>
                    <td className="p-2 text-center">{item.quantity}</td>
                    <td className="p-2 text-center">{item.bonus > 0 ? item.bonus : '-'}</td>
                    <td className="p-2 text-center">{item.exp}</td>
                    <td className="p-2 text-center">{item.price.toLocaleString()}</td>
                    <td className="p-2 text-left">{(item.quantity * item.price).toLocaleString()}</td>
                    <td className="p-2 text-center">{item.notes}</td>
                  </tr>
                ))}
              </tbody>
            </table>
        </div>
      </main>

      <section className="mt-6 flex flex-col sm:flex-row justify-between items-start gap-4">
        <div className="w-full sm:w-2/3 order-2 sm:order-1">
            <h4 className="font-bold mb-2">ملاحظات:</h4>
            <p className="text-xs border p-2 min-h-[60px]">{notes}</p>
            <div className="mt-8 flex justify-around">
                <div className="text-center">
                    <p className="font-bold">--------------------</p>
                    <p>توقيع المستلم</p>
                </div>
                 <div className="text-center">
                    <p className="font-bold">--------------------</p>
                    <p>توقيع المحاسب</p>
                </div>
            </div>
        </div>
        <div className="w-full sm:w-1/3 sm:pr-4 space-y-2 text-sm order-1 sm:order-2">
            <div className="flex justify-between border-b border-gray-400 pb-2">
                <span className="font-bold">المجموع الفرعي:</span>
                <span>{subtotal.toLocaleString()} د.ع</span>
            </div>
             {(totalBonusValue > 0 && invoiceType.includes('بيع')) && (
                <div className="flex justify-between border-b border-gray-400 pb-2 text-destructive">
                    <span className="font-bold">خصم البونص:</span>
                    <span>-&nbsp;{totalBonusValue.toLocaleString()} د.ع</span>
                </div>
             )}
             {(discount > 0) && (
                <div className="flex justify-between border-b border-gray-400 pb-2 text-destructive">
                    <span className="font-bold">الخصم:</span>
                    <span>-&nbsp;{discount.toLocaleString()} د.ع</span>
                </div>
             )}
             <div className="flex justify-between pt-2 text-base sm:text-lg font-bold text-primary">
                <span>{totalReturnedAmount ? 'المبلغ المسترد الصافي:' : 'الإجمالي الصافي:'}</span>
                <span>{displayTotal.toLocaleString()} د.ع</span>
            </div>
        </div>
      </section>

      <footer className="pt-8 mt-8 border-t-2 border-black text-xs">
          <div className="flex justify-between items-end">
              <div className="stamp-area w-24 h-24 border-2 border-dashed flex items-center justify-center text-muted-foreground">
                  <p>مكان الختم</p>
              </div>
              <div className="self-end">
                  <p className="page-number">صفحة 1 من 1</p>
              </div>
          </div>
      </footer>
    </div>
  );
}

    